{"meta": {"version": "1.0", "exported_at": "2025-07-08T22:55:01.807911", "total_candidates": 2, "successful_analyses": 2, "failed_analyses": 0, "job_description": "\nPython & MySQL Developer – Fresher\n📍 Location: Vijayawada, India\n🕒 Job Type: Full-Time | Entry-Level\n🌟 About the Role\nWe’re looking for a passionate and inquisitive fresher eager to kick-start their journey in backend development and data-driven applications. If you're comfortable navigating Python’s data structures and dabbling in MySQL queries, this opportunity is your perfect launchpad!\n\n🚀 Key Responsibilities\nBuild and manage backend logic using Python with a focus on clean data structures\n\nWrite efficient queries to interact with MySQL databases for CRUD operations\n\nCollaborate with frontend developers to integrate APIs and ensure seamless data flow\n\nDebug and optimize backend code for performance and scalability\n\nDocument processes and assist in deployment pipelines\n\n🧠 Required Skills\nSolid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts\n\nFamiliarity with basic MySQL queries, joins, and indexing\n\nExposure to version control systems like Git\n\nGood problem-solving and algorithmic thinking\n\n📚 Nice to Have\nKnowledge of Flask/Django frameworks\n\nAwareness of RESTful APIs\n\nExperience working on small-scale projects or internships\n\n🤝 What We Offer\nMentorship from senior developers\n\nReal-world projects to build your portfolio\n\nA friendly, collaborative work culture\n\nOpportunities for growth into full-stack development roles\n", "processed_at": "2025-07-08T22:55:01.804912"}, "summary_statistics": {"average_score": 24.71, "score_distribution": {"excellent": 0, "good": 0, "average": 0, "below_average": 2}, "recommendations": {"HIRE": 0, "CONSIDER": 0, "REJECT": 2}, "processing_time": 7.03}, "candidates": [{"id": "resume_1", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 9.82, "skills_match": 0, "experience_score": 25.0, "education_score": 0, "keywords_match": 1.3333333333333321, "overall_fit": 33.0, "growth_potential": 50.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks relevant skills and experience for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "Django", "Flask", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["CRUD operations", "Collaboration with frontend developers", "Debugging and optimization", "Documentation and deployment pipelines"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Lack of relevant work experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 6.117036819458008, "processed_at": "2025-07-08T22:55:01.799917", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_2", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 39.6, "skills_match": 45.0, "experience_score": 30.0, "education_score": 20.0, "keywords_match": 48.0, "overall_fit": 60.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Candi<PERSON> has relevant education and some matching skills, but lacks experience with required technologies.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Django", "Flask", "Git"], "skill_match_percentage": 40.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Version control systems like Git not mentioned in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology CGPA: 8.11"], "education_level": "BASIC"}, "assessment": {"strengths": ["Dedicated and passionate coder", "Technologically adept"], "weaknesses": ["Lack of experience with Flask/Django frameworks", "No mention of RESTful APIs"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Flask/Django frameworks", "RESTful APIs"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 6.95815372467041, "processed_at": "2025-07-08T22:55:01.804912", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel in backend development and data-driven applications."}], "top_candidates": [{"id": "resume_2", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 39.6, "skills_match": 45.0, "experience_score": 30.0, "education_score": 20.0, "keywords_match": 48.0, "overall_fit": 60.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Candi<PERSON> has relevant education and some matching skills, but lacks experience with required technologies.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Django", "Flask", "Git"], "skill_match_percentage": 40.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Version control systems like Git not mentioned in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology CGPA: 8.11"], "education_level": "BASIC"}, "assessment": {"strengths": ["Dedicated and passionate coder", "Technologically adept"], "weaknesses": ["Lack of experience with Flask/Django frameworks", "No mention of RESTful APIs"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Flask/Django frameworks", "RESTful APIs"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 6.95815372467041, "processed_at": "2025-07-08T22:55:01.804912", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel in backend development and data-driven applications."}, {"id": "resume_1", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 9.82, "skills_match": 0, "experience_score": 25.0, "education_score": 0, "keywords_match": 1.3333333333333321, "overall_fit": 33.0, "growth_potential": 50.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks relevant skills and experience for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "Django", "Flask", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["CRUD operations", "Collaboration with frontend developers", "Debugging and optimization", "Documentation and deployment pipelines"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Lack of relevant work experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 6.117036819458008, "processed_at": "2025-07-08T22:55:01.799917", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}]}