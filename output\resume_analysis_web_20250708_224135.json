{"meta": {"version": "1.0", "exported_at": "2025-07-08T22:41:35.753680", "total_candidates": 1, "successful_analyses": 1, "failed_analyses": 0, "job_description": "\nSenior Software Developer Position - TechCorp Solutions\n\nPOSITION OVERVIEW:\nWe are seeking a highly skilled Senior Software Developer to join our dynamic engineering team. The ideal candidate will have strong technical expertise, excellent problem-solving abilities, and a passion for building scalable software solutions.\n\nREQUIRED TECHNICAL SKILLS:\n- Programming Languages: Python (3+ years), JavaScript (ES6+), TypeScript\n- Web Frameworks: React.js, Node.js, Express.js, Django/Flask\n- Database Technologies: PostgreSQL, MongoDB, Redis\n- Cloud Platforms: AWS (EC2, S3, RDS, Lambda), Docker containerization\n- Version Control: Git, GitHub/GitLab workflows\n- API Development: RESTful APIs, GraphQL\n- Testing: Unit testing, Integration testing, TDD practices\n\nREQUIRED EXPERIENCE:\n- 3-5 years of professional software development experience\n- Experience with Agile/Scrum development methodologies\n- Proven track record of delivering production-ready applications\n- Experience with CI/CD pipelines and DevOps practices\n- Strong understanding of software architecture patterns\n- Experience with code reviews and mentoring junior developers\n\nREQUIRED EDUCATION & CERTIFICATIONS:\n- Bachelor's degree in Computer Science, Software Engineering, or related technical field\n- Relevant certifications (AWS, Azure, or Google Cloud) preferred\n\nPREFERRED QUALIFICATIONS:\n- Master's degree in Computer Science or related field\n- Experience with microservices architecture\n- Knowledge of machine learning frameworks (TensorFlow, PyTorch)\n- Experience with mobile development (React Native, Flutter)\n- Familiarity with blockchain technologies\n- Open source contributions\n\nSOFT SKILLS:\n- Excellent communication and collaboration skills\n- Strong analytical and problem-solving abilities\n- Ability to work independently and in team environments\n- Leadership potential and mentoring capabilities\n- Adaptability to new technologies and frameworks\n\nRESPONSIBILITIES:\n- Design, develop, and maintain scalable web applications\n- Collaborate with cross-functional teams (Product, Design, QA)\n- Participate in architectural decisions and technical planning\n- Conduct code reviews and ensure code quality standards\n- Troubleshoot and resolve complex technical issues\n- Mentor junior developers and contribute to team knowledge sharing\n- Stay updated with emerging technologies and industry best practices\n\nCOMPANY BENEFITS:\n- Competitive salary range: $90,000 - $130,000\n- Comprehensive health, dental, and vision insurance\n- 401(k) with company matching\n- Flexible work arrangements (hybrid/remote options)\n- Professional development budget ($2,000 annually)\n- Stock options and performance bonuses\n- 25 days PTO + holidays\n", "processed_at": "2025-07-08T22:41:35.751680"}, "summary_statistics": {"average_score": 59.69, "score_distribution": {"excellent": 0, "good": 0, "average": 0, "below_average": 1}, "recommendations": {"HIRE": 0, "CONSIDER": 1, "REJECT": 0}, "processing_time": 7.67}, "candidates": [{"id": "resume_1", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 59.69, "skills_match": 80.0, "experience_score": 45.0, "education_score": 20.0, "keywords_match": 80.6896551724138, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "CONSIDER", "reason": "Lack of relevant technical skills and experience for the Senior Software Developer position.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "JavaScript", "React.js", "Node.js"], "missing_skills": ["Django/Flask", "PostgreSQL", "MongoDB", "Redis", "AWS", "EC2", "S3", "RDS", "Lambda", "Git", "GitHub/GitLab workflows", "API Development", "Testing"], "skill_match_percentage": 23.5}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["unclear"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Excellent communication and collaboration skills", "Strong analytical and problem-solving abilities"], "weaknesses": ["Lack of technical expertise in specific areas"], "red_flags": [], "cultural_fit_indicators": ["unclear"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["unclear"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 7.648840427398682, "processed_at": "2025-07-08T22:41:35.751680", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": "I help founders, CEOs, and coaches to build their strong brand. I have worked with 15+ founders, CEOs, and coaches."}], "top_candidates": [{"id": "resume_1", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 59.69, "skills_match": 80.0, "experience_score": 45.0, "education_score": 20.0, "keywords_match": 80.6896551724138, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "CONSIDER", "reason": "Lack of relevant technical skills and experience for the Senior Software Developer position.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "JavaScript", "React.js", "Node.js"], "missing_skills": ["Django/Flask", "PostgreSQL", "MongoDB", "Redis", "AWS", "EC2", "S3", "RDS", "Lambda", "Git", "GitHub/GitLab workflows", "API Development", "Testing"], "skill_match_percentage": 23.5}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["unclear"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Excellent communication and collaboration skills", "Strong analytical and problem-solving abilities"], "weaknesses": ["Lack of technical expertise in specific areas"], "red_flags": [], "cultural_fit_indicators": ["unclear"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["unclear"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 7.648840427398682, "processed_at": "2025-07-08T22:41:35.751680", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": "I help founders, CEOs, and coaches to build their strong brand. I have worked with 15+ founders, CEOs, and coaches."}]}