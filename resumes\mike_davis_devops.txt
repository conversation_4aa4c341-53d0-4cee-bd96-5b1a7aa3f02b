<PERSON>
DevOps Engineer
Email: <EMAIL>
Phone: (*************
GitHub: github.com/mikedavis

EXPERIENCE:
DevOps Engineer | CloudTech Systems | 2021-2024
- Designed and implemented CI/CD pipelines using Jenkins and GitLab
- Managed AWS infrastructure with Terraform and CloudFormation
- Containerized applications using Docker and orchestrated with Kubernetes
- Implemented monitoring and logging solutions with Prometheus and ELK stack
- Automated deployment processes reducing deployment time by 60%

Systems Administrator | ServerCorp | 2019-2021
- Maintained Linux servers and network infrastructure
- Implemented backup and disaster recovery procedures
- Monitored system performance and resolved technical issues
- Managed database systems (MySQL, PostgreSQL)

EDUCATION:
Bachelor of Science in Computer Engineering
Engineering College | 2015-2019

SKILLS:
Cloud Platforms: AWS, Azure, Google Cloud Platform
Containerization: Docker, Kubernetes, OpenShift
CI/CD: Jenkins, GitLab CI, GitHub Actions
Infrastructure as Code: Terraform, Ansible, CloudFormation
Monitoring: Prometheus, Grafana, ELK Stack, Nagios
Operating Systems: Linux (Ubuntu, CentOS), Windows Server
Scripting: Bash, Python, PowerShell

CERTIFICATIONS:
- AWS Certified Solutions Architect
- Certified Kubernetes Administrator (CKA)
- Docker Certified Associate