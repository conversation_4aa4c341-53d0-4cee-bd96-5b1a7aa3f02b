{"meta": {"version": "1.0", "exported_at": "2025-07-08T23:01:01.778538", "total_candidates": 22, "successful_analyses": 22, "failed_analyses": 0, "job_description": "\nPython & MySQL Developer - Fresher\n📍 Location: Vijayawada, India\n🕒 Job Type: Full-Time | Entry-Level\n🌟 About the Role\nWe’re looking for a passionate and inquisitive fresher eager to kick-start their journey in backend development and data-driven applications. If you're comfortable navigating Python’s data structures and dabbling in MySQL queries, this opportunity is your perfect launchpad!\n\n🚀 Key Responsibilities\nBuild and manage backend logic using Python with a focus on clean data structures\n\nWrite efficient queries to interact with MySQL databases for CRUD operations\n\nCollaborate with frontend developers to integrate APIs and ensure seamless data flow\n\nDebug and optimize backend code for performance and scalability\n\nDocument processes and assist in deployment pipelines\n\n🧠 Required Skills\nSolid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts\n\nFamiliarity with basic MySQL queries, joins, and indexing\n\nExposure to version control systems like Git\n\nGood problem-solving and algorithmic thinking\n\n\n🤝 What We Offer\nMentorship from senior developers\n\nReal-world projects to build your portfolio\n\nA friendly, collaborative work culture\n\nOpportunities for growth into full-stack development roles\n", "processed_at": "2025-07-08T23:01:01.776545"}, "summary_statistics": {"average_score": 38.01, "score_distribution": {"excellent": 0, "good": 0, "average": 0, "below_average": 22}, "recommendations": {"HIRE": 0, "CONSIDER": 11, "REJECT": 11}, "processing_time": 65.52}, "candidates": [{"id": "resume_1", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 59.35, "skills_match": 5, "experience_score": 95.0, "education_score": 60.0, "keywords_match": 83.0, "overall_fit": 85.0, "growth_potential": 95.0}, "recommendation": {"decision": "CONSIDER", "reason": "Strong experience in backend development and problem-solving, but limited exposure to full-stack development.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Backend development", "Data-driven applications"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["NIT, Nagpur"], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem-solving", "Algorithmic thinking"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Full-stack development", "Collaborative work culture"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 11.853647708892822, "processed_at": "2025-07-08T23:01:01.638544", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Experienced software engineer with a strong background in backend development and problem-solving."}, {"id": "resume_2", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 45.87, "skills_match": 0, "experience_score": 80.0, "education_score": 60.0, "keywords_match": 45.333333333333336, "overall_fit": 78.0, "growth_potential": 60.0}, "recommendation": {"decision": "CONSIDER", "reason": "<PERSON>ks relevant experience for the role, but shows potential with good problem-solving skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Solid understanding of Python fundamentals"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Good problem-solving and algorithmic thinking"], "weaknesses": ["Lack of experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Data-driven applications"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 12.616151571273804, "processed_at": "2025-07-08T23:01:01.642543", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software development manager with 6 years of experience in Amazon."}, {"id": "resume_3", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 57.92, "skills_match": 71.66666666666666, "experience_score": 85.0, "education_score": 0, "keywords_match": 39.333333333333336, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "CONSIDER", "reason": "<PERSON>k of required skills and experience for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Exposure to version control systems like Git"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 17.168038845062256, "processed_at": "2025-07-08T23:01:01.655538", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": ""}, {"id": "resume_4", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 11.82, "skills_match": 0, "experience_score": 25.0, "education_score": 0, "keywords_match": 1.3333333333333321, "overall_fit": 53.0, "growth_potential": 40.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the job requirements", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["CRUD operations", "Deployment pipelines"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 6.292381763458252, "processed_at": "2025-07-08T23:01:01.663544", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_5", "filename": "Profile_17.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 31.57, "skills_match": 0, "experience_score": 80.0, "education_score": 0, "keywords_match": 31.333333333333332, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["9 years experience in software engineering"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong problem-solving skills"], "weaknesses": ["Limited experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Python fundamentals"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 6.271728992462158, "processed_at": "2025-07-08T23:01:01.668544", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_17.pdf", "file_size": 0, "word_count": 150, "success": true, "error": null}, "summary": "Experienced software engineer with strong problem-solving skills"}, {"id": "resume_6", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 46.38, "skills_match": 0, "experience_score": 85.0, "education_score": 0, "keywords_match": 86.66666666666667, "overall_fit": 78.0, "growth_potential": 85.0}, "recommendation": {"decision": "CONSIDER", "reason": "Relevant experience and skills, but some missing skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Network path discovery and analysis", "Link inference in large networks based on incomplete data"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven, lean startup practitioner"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Not mentioned"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Network path discovery", "Link inference"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 12.515007734298706, "processed_at": "2025-07-08T23:01:01.681544", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of development experience"}, {"id": "resume_7", "filename": "Profile_14.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 29.07, "skills_match": 0, "experience_score": 70.0, "education_score": 0, "keywords_match": 31.333333333333332, "overall_fit": 53.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate has some relevant training expertise but lacks direct experience in backend development and data-driven applications.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["NLP practitioner", "Corporate training experience"], "experience_gaps": ["No direct experience in backend development or data-driven applications"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["M-Technology Business Development Manager"], "education_level": "BASIC"}, "assessment": {"strengths": ["Training expertise", "Project management skills"], "weaknesses": ["Lack of relevant work experience in Python and MySQL"], "red_flags": [], "cultural_fit_indicators": ["Friendly, collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 17.247315168380737, "processed_at": "2025-07-08T23:01:01.686543", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_14.pdf", "file_size": 0, "word_count": 218, "success": true, "error": null}, "summary": "Experienced HR recruitment specialist with training expertise seeking a role in backend development and data-driven applications."}, {"id": "resume_8", "filename": "Profile_16.pdf", "candidate_name": "<PERSON>", "scores": {"final_score": 38.57, "skills_match": 0, "experience_score": 80.0, "education_score": 20.0, "keywords_match": 41.33333333333333, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Overqualified for entry-level position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Software Development", "CTO", "Director of Software"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Harvard University A.B., Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Leadership experience", "Technical expertise"], "weaknesses": ["<PERSON>k of recent experience in backend development"], "red_flags": [], "cultural_fit_indicators": ["Strong work ethic", "Collaborative mindset"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Transitioning to backend development", "Leadership style"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.14304256439209, "processed_at": "2025-07-08T23:01:01.689544", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_16.pdf", "file_size": 0, "word_count": 120, "success": true, "error": null}, "summary": "Highly experienced software engineer with strong technical skills and leadership experience."}, {"id": "resume_9", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 50.82, "skills_match": 0, "experience_score": 85.0, "education_score": 90.0, "keywords_match": 41.33333333333333, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "CONSIDER", "reason": "Relevant experience, but lack of fit for specific skills required.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Enterprise Integration Architecture", "SOA and Microservices"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.E. (Electronics Telecommunications)"], "education_level": "ADVANCED"}, "assessment": {"strengths": ["Customer focused individual", "Strong understanding of Core Java"], "weaknesses": ["Lack of experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python data structures", "MySQL queries"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 16.92365336418152, "processed_at": "2025-07-08T23:01:01.695544", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with extensive software development experience."}, {"id": "resume_10", "filename": "Profile_18.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 4.3, "skills_match": 0, "experience_score": 10.0, "education_score": 0, "keywords_match": 0, "overall_fit": 18.0, "growth_potential": 30.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate lacks relevant skills and experience for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["No experience in backend development or data-driven applications"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor of Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": ["Senior Software Development Manager experience"], "weaknesses": ["Lack of relevant work experience for the role"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.87079405784607, "processed_at": "2025-07-08T23:01:01.699546", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_18.pdf", "file_size": 0, "word_count": 118, "success": true, "error": null}, "summary": "Senior Software Development Manager with 6 years of experience in Amazon, but no relevant work experience for Python & MySQL Developer role"}, {"id": "resume_11", "filename": "Profile_19.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 20.85, "skills_match": 0, "experience_score": 25.0, "education_score": 0, "keywords_match": 44.0, "overall_fit": 58.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant experience and skills for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Proficient in building distributed, cloud native and server-side applications"], "weaknesses": ["Gaps in backend development and data-driven applications skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Data-driven applications"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.9538676738739, "processed_at": "2025-07-08T23:01:01.707537", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_19.pdf", "file_size": 0, "word_count": 546, "success": true, "error": null}, "summary": "Programmer, architect and technical lead with expertise in C++, Golang, Rust, Linux."}, {"id": "resume_12", "filename": "Profile_20.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 38.8, "skills_match": 0, "experience_score": 80.0, "education_score": 0, "keywords_match": 60.0, "overall_fit": 68.0, "growth_potential": 50.0}, "recommendation": {"decision": "REJECT", "reason": "Relevant HR experience, but lacks backend development skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["No experience in backend development or data-driven applications"], "experience_level": "MID"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["HR professional experience", "E2E Recruitment skills"], "weaknesses": ["Lack of relevant work experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development skills", "Data-driven applications"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 13.600553512573242, "processed_at": "2025-07-08T23:01:01.712624", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_20.pdf", "file_size": 0, "word_count": 232, "success": true, "error": null}, "summary": "HR professional with 2.5 years of experience in IT and non-IT industry"}, {"id": "resume_13", "filename": "Profile_21.pdf", "candidate_name": "Ka<PERSON>ya E.", "scores": {"final_score": 31.3, "skills_match": 0, "experience_score": 80.0, "education_score": 0, "keywords_match": 30.0, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Candi<PERSON> has relevant HR experience but lacks backend development skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["IT hiring experience", "Recruiting strategies implementation"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Easwari Engineering"], "education_level": "BASIC"}, "assessment": {"strengths": ["Talent Acquisition expertise", "Head hunting experience"], "weaknesses": ["Lack of backend development experience"], "red_flags": [], "cultural_fit_indicators": ["Collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development skills", "Recruiting strategies"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.660176753997803, "processed_at": "2025-07-08T23:01:01.716637", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_21.pdf", "file_size": 0, "word_count": 169, "success": true, "error": null}, "summary": "HR Talent Acquisition professional with IT hiring experience and recruiting strategies expertise."}, {"id": "resume_14", "filename": "Profile_2.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 46.13, "skills_match": 0, "experience_score": 80.0, "education_score": 60.0, "keywords_match": 46.666666666666664, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "CONSIDER", "reason": "<PERSON><PERSON> required skills for the position, but has potential with growth support.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["React Js Developer", "Software Engineer <PERSON><PERSON>"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Bachelor of Technology - BTech, Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem-solving skills", "Algorithmic thinking"], "weaknesses": ["Limited experience in backend development"], "red_flags": [], "cultural_fit_indicators": ["Friendly work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 13.611565589904785, "processed_at": "2025-07-08T23:01:01.720623", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_2.pdf", "file_size": 0, "word_count": 119, "success": true, "error": null}, "summary": "Freshers with Python and MySQL experience seeking backend development opportunities."}, {"id": "resume_15", "filename": "Profile_3.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 8.83, "skills_match": 0, "experience_score": 20.0, "education_score": 0, "keywords_match": 2.666666666666666, "overall_fit": 33.0, "growth_potential": 50.0}, "recommendation": {"decision": "REJECT", "reason": "Lack of required technical skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": ["Gaps in relevant technical skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 15.636712312698364, "processed_at": "2025-07-08T23:01:01.726620", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_3.pdf", "file_size": 0, "word_count": 289, "success": true, "error": null}, "summary": ""}, {"id": "resume_16", "filename": "Profile_4.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 19.08, "skills_match": 0, "experience_score": 45.0, "education_score": 0, "keywords_match": 12.666666666666666, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Limited relevant experience for the role", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Backend development experience"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Goal-oriented professional", "Proactive"], "weaknesses": ["Lack of backend development experience"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development experience", "Python and MySQL skills"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 12.428304195404053, "processed_at": "2025-07-08T23:01:01.732611", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_4.pdf", "file_size": 0, "word_count": 419, "success": true, "error": null}, "summary": ""}, {"id": "resume_17", "filename": "Profile_6.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 51.13, "skills_match": 0, "experience_score": 90.0, "education_score": 70.0, "keywords_match": 46.666666666666664, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "CONSIDER", "reason": "Strong leadership skills and AI expertise, but limited backend development experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Software Solutions", "Technical Advocacy", "Generative AI"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["NIT Jalandhar", "Unacademy"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Leadership experience", "AI expertise"], "weaknesses": ["Limited backend development experience"], "red_flags": [], "cultural_fit_indicators": ["Collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Leadership experience", "AI expertise"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 15.806605577468872, "processed_at": "2025-07-08T23:01:01.741614", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_6.pdf", "file_size": 0, "word_count": 524, "success": true, "error": null}, "summary": "Senior Software Engineer with 10+ years of experience in software solutions, technical advocacy, and generative AI."}, {"id": "resume_18", "filename": "Profile_5.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 55.8, "skills_match": 0, "experience_score": 90.0, "education_score": 60.0, "keywords_match": 79.0, "overall_fit": 85.0, "growth_potential": 95.0}, "recommendation": {"decision": "CONSIDER", "reason": "Strong experience with Apache Kafka and Kafka Streams, but lacking in some fundamental Python skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.Tech in Electronics Engineering from IIT BHU"], "education_level": "BASIC"}, "assessment": {"strengths": ["Robust problem-solving", "High-volume data workloads management"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 12.48155426979065, "processed_at": "2025-07-08T23:01:01.746616", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_5.pdf", "file_size": 0, "word_count": 290, "success": true, "error": null}, "summary": "Transformative journey as a Member of Technical Staff at Oracle, with expertise in data processing projects."}, {"id": "resume_19", "filename": "Profile_7.pdf", "candidate_name": "<PERSON><PERSON><PERSON><PERSON>", "scores": {"final_score": 45.08, "skills_match": 0, "experience_score": 95.0, "education_score": 0, "keywords_match": 62.666666666666664, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "CONSIDER", "reason": "Strong foundation in data structures and algorithms, but limited experience in backend development.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Strong foundation in data structures and algorithms", "Passionate competitive programmer"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Data structures and algorithms", "Backend development"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 15.672299146652222, "processed_at": "2025-07-08T23:01:01.755620", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_7.pdf", "file_size": 0, "word_count": 602, "success": true, "error": null}, "summary": "Passionate competitive programmer with a strong foundation in data structures and algorithms."}, {"id": "resume_20", "filename": "Profile_9.pdf", "candidate_name": "Top Skills", "scores": {"final_score": 50.65, "skills_match": 33.33333333333333, "experience_score": 85.0, "education_score": 0, "keywords_match": 58.0, "overall_fit": 78.0, "growth_potential": 90.0}, "recommendation": {"decision": "CONSIDER", "reason": "Relevant experience and skills, but limited fit for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python"], "missing_skills": ["MySQL", "Git"], "skill_match_percentage": 33.3}, "experience_analysis": {"matching_experience": ["Ex- Microsoft", "Ex- Amazon"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": ["Lack of experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Python fundamentals"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 13.457470178604126, "processed_at": "2025-07-08T23:01:01.762617", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Experienced software engineer with a strong background in Android and distributed systems."}, {"id": "resume_21", "filename": "Profile_8.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 34.88, "skills_match": 0, "experience_score": 45.0, "education_score": 20.0, "keywords_match": 76.66666666666667, "overall_fit": 53.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "Lacks relevant skills and experience for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Version control systems like Git"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Merit Certificate for Academic Excellence in Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Good problem-solving skills"], "weaknesses": ["Lack of experience with backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 16.319137573242188, "processed_at": "2025-07-08T23:01:01.769614", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_8.pdf", "file_size": 0, "word_count": 435, "success": true, "error": null}, "summary": "Freshers with strong programming skills and good problem-solving abilities."}, {"id": "resume_22", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 58.1, "skills_match": 71.66666666666666, "experience_score": 50.0, "education_score": 30.0, "keywords_match": 68.0, "overall_fit": 60.0, "growth_potential": 65.0}, "recommendation": {"decision": "CONSIDER", "reason": "Lack of direct experience with MySQL queries and version control systems.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology", "CGPA: 8.11"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 12.15880274772644, "processed_at": "2025-07-08T23:01:01.775615", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel."}], "top_candidates": [{"id": "resume_1", "filename": "Profile_10.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 59.35, "skills_match": 5, "experience_score": 95.0, "education_score": 60.0, "keywords_match": 83.0, "overall_fit": 85.0, "growth_potential": 95.0}, "recommendation": {"decision": "CONSIDER", "reason": "Strong experience in backend development and problem-solving, but limited exposure to full-stack development.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Backend development", "Data-driven applications"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["NIT, Nagpur"], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem-solving", "Algorithmic thinking"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Full-stack development", "Collaborative work culture"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 11.853647708892822, "processed_at": "2025-07-08T23:01:01.638544", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_10.pdf", "file_size": 0, "word_count": 430, "success": true, "error": null}, "summary": "Experienced software engineer with a strong background in backend development and problem-solving."}, {"id": "resume_22", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 58.1, "skills_match": 71.66666666666666, "experience_score": 50.0, "education_score": 30.0, "keywords_match": 68.0, "overall_fit": 60.0, "growth_potential": 65.0}, "recommendation": {"decision": "CONSIDER", "reason": "Lack of direct experience with MySQL queries and version control systems.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology", "CGPA: 8.11"], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 12.15880274772644, "processed_at": "2025-07-08T23:01:01.775615", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel."}, {"id": "resume_3", "filename": "Profile_11.pdf", "candidate_name": "Aishwarya Mahapatra", "scores": {"final_score": 57.92, "skills_match": 71.66666666666666, "experience_score": 85.0, "education_score": 0, "keywords_match": 39.333333333333336, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "CONSIDER", "reason": "<PERSON>k of required skills and experience for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Exposure to version control systems like Git"], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 17.168038845062256, "processed_at": "2025-07-08T23:01:01.655538", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_11.pdf", "file_size": 0, "word_count": 1125, "success": true, "error": null}, "summary": ""}, {"id": "resume_18", "filename": "Profile_5.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 55.8, "skills_match": 0, "experience_score": 90.0, "education_score": 60.0, "keywords_match": 79.0, "overall_fit": 85.0, "growth_potential": 95.0}, "recommendation": {"decision": "CONSIDER", "reason": "Strong experience with Apache Kafka and Kafka Streams, but lacking in some fundamental Python skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.Tech in Electronics Engineering from IIT BHU"], "education_level": "BASIC"}, "assessment": {"strengths": ["Robust problem-solving", "High-volume data workloads management"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 12.48155426979065, "processed_at": "2025-07-08T23:01:01.746616", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_5.pdf", "file_size": 0, "word_count": 290, "success": true, "error": null}, "summary": "Transformative journey as a Member of Technical Staff at Oracle, with expertise in data processing projects."}, {"id": "resume_17", "filename": "Profile_6.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 51.13, "skills_match": 0, "experience_score": 90.0, "education_score": 70.0, "keywords_match": 46.666666666666664, "overall_fit": 88.0, "growth_potential": 95.0}, "recommendation": {"decision": "CONSIDER", "reason": "Strong leadership skills and AI expertise, but limited backend development experience.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Software Solutions", "Technical Advocacy", "Generative AI"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["NIT Jalandhar", "Unacademy"], "education_level": "STANDARD"}, "assessment": {"strengths": ["Leadership experience", "AI expertise"], "weaknesses": ["Limited backend development experience"], "red_flags": [], "cultural_fit_indicators": ["Collaborative work culture"]}, "hiring_insights": {"salary_expectation_alignment": "MEDIUM", "interview_focus_areas": ["Leadership experience", "AI expertise"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 15.806605577468872, "processed_at": "2025-07-08T23:01:01.741614", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_6.pdf", "file_size": 0, "word_count": 524, "success": true, "error": null}, "summary": "Senior Software Engineer with 10+ years of experience in software solutions, technical advocacy, and generative AI."}, {"id": "resume_9", "filename": "Profile_15.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 50.82, "skills_match": 0, "experience_score": 85.0, "education_score": 90.0, "keywords_match": 41.33333333333333, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "CONSIDER", "reason": "Relevant experience, but lack of fit for specific skills required.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Enterprise Integration Architecture", "SOA and Microservices"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": ["B.E. (Electronics Telecommunications)"], "education_level": "ADVANCED"}, "assessment": {"strengths": ["Customer focused individual", "Strong understanding of Core Java"], "weaknesses": ["Lack of experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python data structures", "MySQL queries"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 16.92365336418152, "processed_at": "2025-07-08T23:01:01.695544", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_15.pdf", "file_size": 0, "word_count": 318, "success": true, "error": null}, "summary": "Customer focused individual with extensive software development experience."}, {"id": "resume_20", "filename": "Profile_9.pdf", "candidate_name": "Top Skills", "scores": {"final_score": 50.65, "skills_match": 33.33333333333333, "experience_score": 85.0, "education_score": 0, "keywords_match": 58.0, "overall_fit": 78.0, "growth_potential": 90.0}, "recommendation": {"decision": "CONSIDER", "reason": "Relevant experience and skills, but limited fit for the role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python"], "missing_skills": ["MySQL", "Git"], "skill_match_percentage": 33.3}, "experience_analysis": {"matching_experience": ["Ex- Microsoft", "Ex- Amazon"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Innovation", "Scalability"], "weaknesses": ["Lack of experience in backend development"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Python fundamentals"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 13.457470178604126, "processed_at": "2025-07-08T23:01:01.762617", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_9.pdf", "file_size": 0, "word_count": 383, "success": true, "error": null}, "summary": "Experienced software engineer with a strong background in Android and distributed systems."}, {"id": "resume_6", "filename": "Profile_13.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 46.38, "skills_match": 0, "experience_score": 85.0, "education_score": 0, "keywords_match": 86.66666666666667, "overall_fit": 78.0, "growth_potential": 85.0}, "recommendation": {"decision": "CONSIDER", "reason": "Relevant experience and skills, but some missing skills", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["Network path discovery and analysis", "Link inference in large networks based on incomplete data"], "experience_gaps": [], "experience_level": "SENIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Data driven, lean startup practitioner"], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": ["Not mentioned"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Network path discovery", "Link inference"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 12.515007734298706, "processed_at": "2025-07-08T23:01:01.681544", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_13.pdf", "file_size": 0, "word_count": 957, "success": true, "error": null}, "summary": "Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of development experience"}, {"id": "resume_14", "filename": "Profile_2.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 46.13, "skills_match": 0, "experience_score": 80.0, "education_score": 60.0, "keywords_match": 46.666666666666664, "overall_fit": 78.0, "growth_potential": 80.0}, "recommendation": {"decision": "CONSIDER", "reason": "<PERSON><PERSON> required skills for the position, but has potential with growth support.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": ["React Js Developer", "Software Engineer <PERSON><PERSON>"], "experience_gaps": [], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Bachelor of Technology - BTech, Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Problem-solving skills", "Algorithmic thinking"], "weaknesses": ["Limited experience in backend development"], "red_flags": [], "cultural_fit_indicators": ["Friendly work culture"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Python fundamentals", "MySQL queries"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 13.611565589904785, "processed_at": "2025-07-08T23:01:01.720623", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_2.pdf", "file_size": 0, "word_count": 119, "success": true, "error": null}, "summary": "Freshers with Python and MySQL experience seeking backend development opportunities."}, {"id": "resume_2", "filename": "Profile_12.pdf", "candidate_name": "<PERSON><PERSON>", "scores": {"final_score": 45.87, "skills_match": 0, "experience_score": 80.0, "education_score": 60.0, "keywords_match": 45.333333333333336, "overall_fit": 78.0, "growth_potential": 60.0}, "recommendation": {"decision": "CONSIDER", "reason": "<PERSON>ks relevant experience for the role, but shows potential with good problem-solving skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Solid understanding of Python fundamentals"], "experience_level": "MID"}, "education_analysis": {"education_highlights": ["Masters degree, Computer Science"], "education_level": "BASIC"}, "assessment": {"strengths": ["Good problem-solving and algorithmic thinking"], "weaknesses": ["Lack of experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Backend development", "Data-driven applications"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 12.616151571273804, "processed_at": "2025-07-08T23:01:01.642543", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_12.pdf", "file_size": 0, "word_count": 131, "success": true, "error": null}, "summary": "Software development manager with 6 years of experience in Amazon."}]}