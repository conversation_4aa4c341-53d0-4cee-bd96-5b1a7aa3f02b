{"meta": {"version": "1.0", "exported_at": "2025-07-08T22:21:08.087018", "total_candidates": 1, "successful_analyses": 1, "failed_analyses": 0, "job_description": "\nSenior Software Developer Position - TechCorp Solutions\n\nPOSITION OVERVIEW:\nWe are seeking a highly skilled Senior Software Developer to join our dynamic engineering team. The ideal candidate will have strong technical expertise, excellent problem-solving abilities, and a passion for building scalable software solutions.\n\nREQUIRED TECHNICAL SKILLS:\n- Programming Languages: Python (3+ years), JavaScript (ES6+), TypeScript\n- Web Frameworks: React.js, Node.js, Express.js, Django/Flask\n- Database Technologies: PostgreSQL, MongoDB, Redis\n- Cloud Platforms: AWS (EC2, S3, RDS, Lambda), Docker containerization\n- Version Control: Git, GitHub/GitLab workflows\n- API Development: RESTful APIs, GraphQL\n- Testing: Unit testing, Integration testing, TDD practices\n\nREQUIRED EXPERIENCE:\n- 3-5 years of professional software development experience\n- Experience with Agile/Scrum development methodologies\n- Proven track record of delivering production-ready applications\n- Experience with CI/CD pipelines and DevOps practices\n- Strong understanding of software architecture patterns\n- Experience with code reviews and mentoring junior developers\n\nREQUIRED EDUCATION & CERTIFICATIONS:\n- Bachelor's degree in Computer Science, Software Engineering, or related technical field\n- Relevant certifications (AWS, Azure, or Google Cloud) preferred\n\nPREFERRED QUALIFICATIONS:\n- Master's degree in Computer Science or related field\n- Experience with microservices architecture\n- Knowledge of machine learning frameworks (TensorFlow, PyTorch)\n- Experience with mobile development (React Native, Flutter)\n- Familiarity with blockchain technologies\n- Open source contributions\n\nSOFT SKILLS:\n- Excellent communication and collaboration skills\n- Strong analytical and problem-solving abilities\n- Ability to work independently and in team environments\n- Leadership potential and mentoring capabilities\n- Adaptability to new technologies and frameworks\n\nRESPONSIBILITIES:\n- Design, develop, and maintain scalable web applications\n- Collaborate with cross-functional teams (Product, Design, QA)\n- Participate in architectural decisions and technical planning\n- Conduct code reviews and ensure code quality standards\n- Troubleshoot and resolve complex technical issues\n- Mentor junior developers and contribute to team knowledge sharing\n- Stay updated with emerging technologies and industry best practices\n\nCOMPANY BENEFITS:\n- Competitive salary range: $90,000 - $130,000\n- Comprehensive health, dental, and vision insurance\n- 401(k) with company matching\n- Flexible work arrangements (hybrid/remote options)\n- Professional development budget ($2,000 annually)\n- Stock options and performance bonuses\n- 25 days PTO + holidays\n", "processed_at": "2025-07-08T22:21:08.085820"}, "summary_statistics": {"average_score": 37.69, "score_distribution": {"excellent": 0, "good": 0, "average": 0, "below_average": 1}, "recommendations": {"HIRE": 0, "CONSIDER": 0, "REJECT": 1}, "processing_time": 11.0}, "candidates": [{"id": "resume_1", "filename": "Profile_1.pdf", "candidate_name": "Name not found", "scores": {"final_score": 37.69, "skills_match": 40.0, "experience_score": 35.0, "education_score": 20.0, "keywords_match": 40.689655172413794, "overall_fit": 58.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "While <PERSON><PERSON><PERSON> has a strong background in personal branding and marketing, her technical expertise does not align with the job requirements. Her career path is unconventional, and she lacks experience in areas such as microservices architecture, machine learning frameworks, and mobile development. However, she demonstrates excellent communication skills, analytical abilities, and adaptability to new technologies, which are valuable assets for a software developer role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "JavaScript", "React.js", "Node.js", "Django/Flask"], "missing_skills": ["TypeScript (less than 3 years)", "PostgreSQL (less than 1 year)", "AWS (EC2, S3, RDS, Lambda) (less than 1 year)", "Git (less than 5 years)", "GraphQL", "TDD practices (less than 3 years)"], "skill_match_percentage": 45.5}, "experience_analysis": {"matching_experience": ["Experience with Agile/Scrum development methodologies", "Proven track record of delivering production-ready applications"], "experience_gaps": ["CI/CD pipelines and DevOps practices (less than 1 year)", "Strong understanding of software architecture patterns (less than 2 years)", "Code reviews and mentoring junior developers (less than 1 year)"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science", "Relevant certifications (AWS, Azure, or Google Cloud)"], "education_level": "BASIC"}, "assessment": {"strengths": ["Excellent communication skills", "Strong analytical abilities", "Ability to work independently and in team environments"], "weaknesses": ["Lack of experience with microservices architecture", "Limited knowledge of machine learning frameworks", "No experience with mobile development"], "red_flags": ["Unconventional career path (Personal Branding Strategist)", "Limited technical expertise compared to the job requirements"], "cultural_fit_indicators": ["Adaptability to new technologies and frameworks", "Leadership potential and mentoring capabilities"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills alignment", "Career path and unconventional experience", "Leadership and collaboration indicators"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 10.979550838470459, "processed_at": "2025-07-08T22:21:08.084806", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": "<PERSON><PERSON><PERSON> has a unique background in personal branding and marketing, but her technical expertise does not meet the job requirements. She lacks experience in areas such as microservices architecture, machine learning frameworks, and mobile development, but demonstrates strong communication skills, analytical abilities, and adaptability to new technologies."}], "top_candidates": [{"id": "resume_1", "filename": "Profile_1.pdf", "candidate_name": "Name not found", "scores": {"final_score": 37.69, "skills_match": 40.0, "experience_score": 35.0, "education_score": 20.0, "keywords_match": 40.689655172413794, "overall_fit": 58.0, "growth_potential": 70.0}, "recommendation": {"decision": "REJECT", "reason": "While <PERSON><PERSON><PERSON> has a strong background in personal branding and marketing, her technical expertise does not align with the job requirements. Her career path is unconventional, and she lacks experience in areas such as microservices architecture, machine learning frameworks, and mobile development. However, she demonstrates excellent communication skills, analytical abilities, and adaptability to new technologies, which are valuable assets for a software developer role.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "JavaScript", "React.js", "Node.js", "Django/Flask"], "missing_skills": ["TypeScript (less than 3 years)", "PostgreSQL (less than 1 year)", "AWS (EC2, S3, RDS, Lambda) (less than 1 year)", "Git (less than 5 years)", "GraphQL", "TDD practices (less than 3 years)"], "skill_match_percentage": 45.5}, "experience_analysis": {"matching_experience": ["Experience with Agile/Scrum development methodologies", "Proven track record of delivering production-ready applications"], "experience_gaps": ["CI/CD pipelines and DevOps practices (less than 1 year)", "Strong understanding of software architecture patterns (less than 2 years)", "Code reviews and mentoring junior developers (less than 1 year)"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor's degree in Computer Science", "Relevant certifications (AWS, Azure, or Google Cloud)"], "education_level": "BASIC"}, "assessment": {"strengths": ["Excellent communication skills", "Strong analytical abilities", "Ability to work independently and in team environments"], "weaknesses": ["Lack of experience with microservices architecture", "Limited knowledge of machine learning frameworks", "No experience with mobile development"], "red_flags": ["Unconventional career path (Personal Branding Strategist)", "Limited technical expertise compared to the job requirements"], "cultural_fit_indicators": ["Adaptability to new technologies and frameworks", "Leadership potential and mentoring capabilities"]}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills alignment", "Career path and unconventional experience", "Leadership and collaboration indicators"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 10.979550838470459, "processed_at": "2025-07-08T22:21:08.084806", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": "<PERSON><PERSON><PERSON> has a unique background in personal branding and marketing, but her technical expertise does not meet the job requirements. She lacks experience in areas such as microservices architecture, machine learning frameworks, and mobile development, but demonstrates strong communication skills, analytical abilities, and adaptability to new technologies."}]}