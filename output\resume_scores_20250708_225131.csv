filename,candidate_name,final_score,recommendation,recommendation_reason,skills_match_score,experience_score,education_score,keywords_match_score,overall_fit_score,growth_potential_score,matching_skills,missing_skills,matching_experience,experience_gaps,education_highlights,strengths,weaknesses,red_flags,cultural_fit_indicators,salary_expectation_alignment,interview_focus_areas,summary,processing_time,success,error,file_path
Profile_1.pdf,<PERSON><PERSON><PERSON>,1.55,REJECT,Lack of relevant technical skills and experience for the position,0,5,0,0,3,0,,Python; JavaScript; TypeScript; React; Node.js; Express; Django; Flask; PostgreSQL; MongoDB; Redis; AWS; Azure; Google Cloud; Docker; TensorFlow; PyTorch; React Native; Flutter; Git; GraphQL; Microservices; API; Agile; Scrum; TDD; CI/CD; DevOps,,Required experience not found in resume,,,,,,LOW,,,5.387444019317627,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_1.pdf
resume.pdf,AKURATHI SASIDHAR,24.26,REJECT,Candidate lacks relevant technical skills and experience for the position.,8.571428571428571,40.0,10.0,23.448275862068968,55.0,30.0,Python,JavaScript; TypeScript; React; Node.js; Express; Django; Flask; PostgreSQL; MongoDB; Redis; AWS; Azure; Google Cloud; Docker; TensorFlow; PyTorch; React Native; Flutter; Git; GraphQL; Microservices; API; Agile; Scrum; TDD; CI/CD; DevOps,,3-5 years of professional software development experience,Bachelor Of Technology,Positive attitude; Willingness and motivation to quickly learn new programs,Lack of relevant technical skills,,,LOW,Lack of relevant technical skills; Insufficient experience,Dedicated and passionate coder with a strong desire to excel.,7.464153051376343,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\resume.pdf
