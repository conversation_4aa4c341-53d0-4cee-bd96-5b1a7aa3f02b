filename,candidate_name,final_score,recommendation,recommendation_reason,skills_match_score,experience_score,education_score,keywords_match_score,overall_fit_score,growth_potential_score,matching_skills,missing_skills,matching_experience,experience_gaps,education_highlights,strengths,weaknesses,red_flags,cultural_fit_indicators,salary_expectation_alignment,interview_focus_areas,summary,processing_time,success,error,file_path
Profile_1.pdf,<PERSON><PERSON><PERSON>,9.82,REJECT,Lacks relevant skills and experience for the position,0,25.0,0,1.3333333333333321,33.0,50.0,,Python; Django; Flask; MySQL; Git,,CRUD operations; Collaboration with frontend developers; Debugging and optimization; Documentation and deployment pipelines,,,Lack of relevant work experience in backend development and data-driven applications,,,LOW,,,6.117036819458008,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_1.pdf
resume.pdf,AKURATHI SASIDHAR,39.6,REJECT,"Candi<PERSON> has relevant education and some matching skills, but lacks experience with required technologies.",45.0,30.0,20.0,48.0,60.0,70.0,Python; MySQL,Django; Flask; Git,,Version control systems like Git not mentioned in resume,Bachelor Of Technology CGPA: 8.11,Dedicated and passionate coder; Technologically adept,Lack of experience with Flask/Django frameworks; No mention of RESTful APIs,,,LOW,Flask/Django frameworks; RESTful APIs,Dedicated and passionate coder with a strong desire to excel in backend development and data-driven applications.,6.95815372467041,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\resume.pdf
