{"meta": {"version": "1.0", "exported_at": "2025-07-08T22:56:30.935044", "total_candidates": 2, "successful_analyses": 2, "failed_analyses": 0, "job_description": "\nPython & MySQL Developer - Fresher\n📍 Location: Vijayawada, India\n🕒 Job Type: Full-Time | Entry-Level\n🌟 About the Role\nWe’re looking for a passionate and inquisitive fresher eager to kick-start their journey in backend development and data-driven applications. If you're comfortable navigating Python’s data structures and dabbling in MySQL queries, this opportunity is your perfect launchpad!\n\n🚀 Key Responsibilities\nBuild and manage backend logic using Python with a focus on clean data structures\n\nWrite efficient queries to interact with MySQL databases for CRUD operations\n\nCollaborate with frontend developers to integrate APIs and ensure seamless data flow\n\nDebug and optimize backend code for performance and scalability\n\nDocument processes and assist in deployment pipelines\n\n🧠 Required Skills\nSolid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts\n\nFamiliarity with basic MySQL queries, joins, and indexing\n\nExposure to version control systems like Git\n\nGood problem-solving and algorithmic thinking\n\n\n🤝 What We Offer\nMentorship from senior developers\n\nReal-world projects to build your portfolio\n\nA friendly, collaborative work culture\n\nOpportunities for growth into full-stack development roles\n", "processed_at": "2025-07-08T22:56:30.933041"}, "summary_statistics": {"average_score": 40.21, "score_distribution": {"excellent": 0, "good": 0, "average": 0, "below_average": 2}, "recommendations": {"HIRE": 0, "CONSIDER": 1, "REJECT": 1}, "processing_time": 5.9}, "candidates": [{"id": "resume_1", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 32.82, "skills_match": 0, "experience_score": 45.0, "education_score": 0, "keywords_match": 71.33333333333333, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Not suitable for the role due to lack of relevant experience and skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Solid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Good problem-solving and algorithmic thinking"], "weaknesses": ["Lack of experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.689244985580444, "processed_at": "2025-07-08T22:56:30.928023", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_2", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 47.6, "skills_match": 71.66666666666666, "experience_score": 30.0, "education_score": 20.0, "keywords_match": 48.0, "overall_fit": 60.0, "growth_potential": 70.0}, "recommendation": {"decision": "CONSIDER", "reason": "<PERSON><PERSON> required technical skills for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology"], "education_level": "BASIC"}, "assessment": {"strengths": ["Dedicated and passionate coder", "Technologically adept"], "weaknesses": ["Gaps in technical skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills", "Problem-solving abilities"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 5.833555459976196, "processed_at": "2025-07-08T22:56:30.933041", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel"}], "top_candidates": [{"id": "resume_2", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 47.6, "skills_match": 71.66666666666666, "experience_score": 30.0, "education_score": 20.0, "keywords_match": 48.0, "overall_fit": 60.0, "growth_potential": 70.0}, "recommendation": {"decision": "CONSIDER", "reason": "<PERSON><PERSON> required technical skills for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python", "MySQL"], "missing_skills": ["Git"], "skill_match_percentage": 66.7}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology"], "education_level": "BASIC"}, "assessment": {"strengths": ["Dedicated and passionate coder", "Technologically adept"], "weaknesses": ["Gaps in technical skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Technical skills", "Problem-solving abilities"], "onboarding_priority": "MEDIUM"}, "metadata": {"processing_time": 5.833555459976196, "processed_at": "2025-07-08T22:56:30.933041", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel"}, {"id": "resume_1", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 32.82, "skills_match": 0, "experience_score": 45.0, "education_score": 0, "keywords_match": 71.33333333333333, "overall_fit": 73.0, "growth_potential": 80.0}, "recommendation": {"decision": "REJECT", "reason": "Not suitable for the role due to lack of relevant experience and skills.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "MySQL", "Git"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Solid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": ["Good problem-solving and algorithmic thinking"], "weaknesses": ["Lack of experience in backend development and data-driven applications"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.689244985580444, "processed_at": "2025-07-08T22:56:30.928023", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}]}