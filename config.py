"""
Configuration settings for Resume Parser and Scoring System
"""

import os
from pathlib import Path

# Project paths
PROJECT_ROOT = Path(__file__).parent
RESUMES_DIR = PROJECT_ROOT / "resumes"
OUTPUT_DIR = PROJECT_ROOT / "output"
TEMP_DIR = PROJECT_ROOT / "temp"

# Ollama configuration
OLLAMA_BASE_URL = "http://localhost:11434"
OLLAMA_MODEL = "llama3.2:3b"
OLLAMA_TIMEOUT = 120  # seconds

# Scoring configuration
MAX_SCORE = 100
MIN_SCORE = 0
PASSING_SCORE = 60

# Supported file formats
SUPPORTED_FORMATS = ['.pdf', '.docx', '.doc', '.txt']

# Resume parsing settings
MAX_FILE_SIZE_MB = 10
CHUNK_SIZE = 1000  # characters for LLM processing

# Output settings
OUTPUT_FORMATS = ['csv', 'json', 'xlsx']
DEFAULT_OUTPUT_FORMAT = 'csv'

# Create necessary directories
def create_directories():
    """Create necessary project directories if they don't exist"""
    directories = [RESUMES_DIR, OUTPUT_DIR, TEMP_DIR]
    for directory in directories:
        directory.mkdir(exist_ok=True)

# Scoring criteria weights (should sum to 1.0)
SCORING_WEIGHTS = {
    'skills_match': 0.30,
    'experience_relevance': 0.25,
    'education_match': 0.15,
    'keywords_match': 0.20,
    'overall_fit': 0.10
}

# Default job description template
DEFAULT_JOB_DESCRIPTION = """
Senior Software Developer Position - TechCorp Solutions

POSITION OVERVIEW:
We are seeking a highly skilled Senior Software Developer to join our dynamic engineering team. The ideal candidate will have strong technical expertise, excellent problem-solving abilities, and a passion for building scalable software solutions.

REQUIRED TECHNICAL SKILLS:
- Programming Languages: Python (3+ years), JavaScript (ES6+), TypeScript
- Web Frameworks: React.js, Node.js, Express.js, Django/Flask
- Database Technologies: PostgreSQL, MongoDB, Redis
- Cloud Platforms: AWS (EC2, S3, RDS, Lambda), Docker containerization
- Version Control: Git, GitHub/GitLab workflows
- API Development: RESTful APIs, GraphQL
- Testing: Unit testing, Integration testing, TDD practices

REQUIRED EXPERIENCE:
- 3-5 years of professional software development experience
- Experience with Agile/Scrum development methodologies
- Proven track record of delivering production-ready applications
- Experience with CI/CD pipelines and DevOps practices
- Strong understanding of software architecture patterns
- Experience with code reviews and mentoring junior developers

REQUIRED EDUCATION & CERTIFICATIONS:
- Bachelor's degree in Computer Science, Software Engineering, or related technical field
- Relevant certifications (AWS, Azure, or Google Cloud) preferred

PREFERRED QUALIFICATIONS:
- Master's degree in Computer Science or related field
- Experience with microservices architecture
- Knowledge of machine learning frameworks (TensorFlow, PyTorch)
- Experience with mobile development (React Native, Flutter)
- Familiarity with blockchain technologies
- Open source contributions

SOFT SKILLS:
- Excellent communication and collaboration skills
- Strong analytical and problem-solving abilities
- Ability to work independently and in team environments
- Leadership potential and mentoring capabilities
- Adaptability to new technologies and frameworks

RESPONSIBILITIES:
- Design, develop, and maintain scalable web applications
- Collaborate with cross-functional teams (Product, Design, QA)
- Participate in architectural decisions and technical planning
- Conduct code reviews and ensure code quality standards
- Troubleshoot and resolve complex technical issues
- Mentor junior developers and contribute to team knowledge sharing
- Stay updated with emerging technologies and industry best practices

COMPANY BENEFITS:
- Competitive salary range: $90,000 - $130,000
- Comprehensive health, dental, and vision insurance
- 401(k) with company matching
- Flexible work arrangements (hybrid/remote options)
- Professional development budget ($2,000 annually)
- Stock options and performance bonuses
- 25 days PTO + holidays
"""
