# Bulk Resume Parser and Scoring System

A Python-based system for parsing and scoring resumes against job descriptions using Ollama LLM (llama3.2:3b).

## Features

- **Bulk Resume Processing**: Process multiple resumes in PDF, DOCX, DOC, and TXT formats
- **AI-Powered Scoring**: Uses Ollama LLM for intelligent resume analysis and scoring
- **Job Description Matching**: Compares resumes against provided job descriptions
- **Multiple Output Formats**: Export results in CSV, JSON, or Excel formats
- **Command Line Interface**: Easy-to-use CLI for batch processing
- **Detailed Scoring**: Provides breakdown of scores across different criteria

## System Requirements

- Python 3.8+
- Ollama installed and running locally
- llama3.2:3b model downloaded in Ollama
- 8GB+ RAM recommended (your 24GB is perfect!)
- SSD storage for faster processing

## 🚀 STEP-BY-STEP INSTALLATION TUTORIAL

### Step 1: Install Ollama

1. **Download Ollama** from https://ollama.ai/
2. **Install** following the instructions for your OS
3. **Start Ollama service**:
   ```bash
   ollama serve
   ```
   Keep this terminal open - <PERSON><PERSON><PERSON> needs to run in the background.

### Step 2: Install the LLM Model

In a new terminal, pull the llama3.2:3b model:
```bash
ollama pull llama3.2:3b
```
This will download ~2GB. With your RTX 4050 and 24GB RAM, this model will run smoothly.

### Step 3: Set Up the Python Project

1. **Clone or download** this project to your desired location
2. **Navigate** to the project directory:
   ```bash
   cd "Test Resume Parser"
   ```

3. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

### Step 4: Quick Setup (Automated)

Run the automated setup script:
```bash
python setup_project.py
```

This will:
- Install all dependencies
- Check Ollama connection
- Create sample resume files
- Create sample job descriptions
- Set up project directories

### Step 5: Test the Installation

```bash
python main.py setup
```

This command will verify everything is working correctly.

## 🎯 QUICK START GUIDE

### Option 1: Process Sample Data (Recommended for First Run)

```bash
# Process sample resumes with sample job description
python main.py process --job-description-file software_developer_job.txt
```

### Option 2: Process Your Own Resumes

1. **Place your resume files** in the `resumes/` directory
2. **Run with custom job description**:
   ```bash
   python main.py process --job-description "Your job description here"
   ```

### Option 3: Process Specific Files

```bash
python main.py score resume1.pdf resume2.docx --job-description "Job description"
```

## 📖 DETAILED USAGE EXAMPLES

### Basic Usage
```bash
# Process resumes in default directory with inline job description
python main.py process --job-description "Software Developer with Python and React experience"
```

### Advanced Usage
```bash
# Full customization
python main.py process \
  --job-description-file job_desc.txt \
  --resumes-dir ./my_resumes \
  --output-dir ./results \
  --format xlsx \
  --min-score 70 \
  --max-workers 1 \
  --verbose
```

### Specific File Processing
```bash
# Score specific resume files
python main.py score resume1.pdf resume2.docx resume3.txt \
  --job-description "Data Scientist with Python and ML experience" \
  --format json
```

### Performance Optimization for Your System

Given your specs (i5-13450HX, 24GB RAM, RTX 4050):
- Use `--max-workers 2` for optimal performance
- The RTX 4050 will accelerate LLM inference
- 24GB RAM allows processing large batches without issues

```bash
# Optimized for your hardware
python main.py process \
  --job-description-file software_developer_job.txt \
  --max-workers 2 \
  --format xlsx
```

## Project Structure

```
├── main.py              # Main CLI application
├── config.py            # Configuration settings
├── resume_parser.py     # Resume parsing functionality
├── ollama_client.py     # Ollama LLM integration
├── scoring_engine.py    # Resume scoring logic
├── bulk_processor.py    # Batch processing system
├── export_utils.py      # Export functionality
├── resumes/             # Input resume directory
├── output/              # Output results directory
└── temp/                # Temporary files
```

## Configuration

Edit `config.py` to customize:
- Ollama model and URL
- Scoring weights and criteria
- File size limits
- Output formats

## 🎯 Enhanced Scoring Criteria

The system evaluates resumes based on:
- **Skills Match** (30%): Relevance of technical and soft skills
- **Experience Relevance** (25%): Work experience alignment
- **Education Match** (15%): Educational background fit
- **Keywords Match** (20%): Job-specific keyword presence
- **Overall Fit** (10%): General suitability assessment

## 📊 Comprehensive Output Format

### Core Results:
- Resume filename and overall score (0-100)
- Individual criterion scores with detailed breakdown
- Final recommendation (HIRE/CONSIDER/REJECT) with reasoning

### 🆕 Enhanced Analysis Columns:
- **Matching Skills**: Specific skills that align with job requirements
- **Missing Skills**: Key skills gaps that need to be addressed
- **Matching Experience**: Relevant work experience highlights
- **Experience Gaps**: Areas where experience is lacking
- **Education Highlights**: Relevant degrees, certifications, courses
- **Growth Potential**: Assessment of candidate's learning and advancement potential
- **Cultural Fit Indicators**: Soft skills and team collaboration evidence
- **Red Flags**: Potential concerns or warning signs
- **Salary Expectation Alignment**: LOW/MEDIUM/HIGH alignment with budget
- **Interview Focus Areas**: Suggested topics for technical interviews

### Professional Insights:
- Detailed strengths and weaknesses analysis
- Comprehensive candidate summary
- Actionable recommendations for hiring decisions
- Processing time and success metrics

## 🔧 TROUBLESHOOTING

### Common Issues and Solutions

1. **Ollama not responding**:
   ```bash
   # Check if Ollama is running
   ollama list

   # If not running, start it
   ollama serve
   ```

2. **Model not found**:
   ```bash
   # Pull the required model
   ollama pull llama3.2:3b

   # Verify it's installed
   ollama list
   ```

3. **File parsing errors**:
   - Check file format (PDF, DOCX, DOC, TXT only)
   - Ensure file size is under 10MB
   - Verify file is not corrupted

4. **Memory issues** (unlikely with your 24GB RAM):
   ```bash
   # Reduce concurrent workers
   python main.py process --max-workers 1
   ```

5. **Slow processing**:
   - Ensure Ollama is using GPU acceleration
   - Check system resources with Task Manager
   - Consider processing smaller batches

6. **Permission errors**:
   ```bash
   # Run with administrator privileges if needed
   # Or check file/folder permissions
   ```

### Performance Tips for Your System

- **GPU Acceleration**: Your RTX 4050 should automatically accelerate Ollama
- **RAM Usage**: 24GB allows processing large batches efficiently
- **SSD Speed**: Your NVMe SSD will speed up file I/O operations
- **CPU Optimization**: i5-13450HX handles multi-threading well

### Getting Help

If you encounter issues:
1. Run with `--verbose` flag for detailed logs
2. Check the `output/` directory for error logs
3. Verify all dependencies are installed correctly

## 📊 EXPECTED PERFORMANCE

With your hardware specifications:
- **Processing Speed**: ~30-60 seconds per resume
- **Batch Processing**: 10-20 resumes in parallel efficiently
- **Memory Usage**: ~2-4GB during processing
- **GPU Utilization**: RTX 4050 will accelerate LLM inference

## 🎯 NEXT STEPS

After successful setup:
1. **Customize scoring weights** in `config.py`
2. **Add your own job descriptions**
3. **Integrate with your existing workflow**
4. **Explore the API for programmatic usage**

## 📝 License

MIT License - Feel free to modify and use in your projects!
