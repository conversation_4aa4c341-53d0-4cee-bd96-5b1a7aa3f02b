"""
Resume Scoring Engine
Combines LLM analysis with rule-based scoring for comprehensive resume evaluation
"""

import logging
import re
from typing import Dict, List, Any, Optional
from datetime import datetime

from ollama_client import <PERSON>llamaClient
from config import SCORING_WEIGHTS, MAX_SCORE, MIN_SCORE, PASSING_SCORE, DEFAULT_THREAD_POOL_SIZE

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ScoringEngine:
    """Main scoring engine that combines LLM analysis with rule-based scoring"""

    def __init__(self, thread_pool_size: int = DEFAULT_THREAD_POOL_SIZE):
        self.ollama_client = OllamaClient(thread_pool_size=thread_pool_size)
        self.weights = SCORING_WEIGHTS
        self.max_score = MAX_SCORE
        self.min_score = MIN_SCORE
        self.passing_score = PASSING_SCORE
        self.thread_pool_size = thread_pool_size
    
    def score_resume(self, resume_data: Dict[str, Any], job_description: str) -> Dict[str, Any]:
        """
        Score a resume against a job description
        
        Args:
            resume_data (Dict): Parsed resume data from resume_parser
            job_description (str): Job description to match against
            
        Returns:
            Dict containing comprehensive scoring results
        """
        
        if not resume_data.get('success', False):
            return {
                'success': False,
                'error': resume_data.get('error', 'Resume parsing failed'),
                'filename': resume_data.get('filename', 'unknown'),
                'scores': {},
                'recommendation': 'REJECT'
            }
        
        try:
            # Get LLM analysis
            llm_result = self.ollama_client.analyze_resume(
                resume_data['text'], 
                job_description
            )
            
            if not llm_result['success']:
                logger.error(f"LLM analysis failed: {llm_result['error']}")
                return {
                    'success': False,
                    'error': f"LLM analysis failed: {llm_result['error']}",
                    'filename': resume_data['filename'],
                    'scores': {},
                    'recommendation': 'REJECT'
                }
            
            # Improve skill matching accuracy
            improved_analysis = self._improve_skill_matching(
                llm_result['analysis'],
                resume_data['text'],
                job_description
            )

            # Combine LLM analysis with rule-based scoring
            combined_scores = self._combine_scores(
                improved_analysis,
                resume_data,
                job_description
            )
            
            # Calculate final weighted score
            final_score = self._calculate_weighted_score(combined_scores)
            
            # Generate recommendation
            recommendation = self._generate_recommendation(final_score, combined_scores)

            # Get the best candidate name (LLM extracted or parser extracted)
            llm_name = improved_analysis.get('candidate_name', '')
            parser_name = resume_data.get('metadata', {}).get('candidate_name', 'Name not found')

            # Use LLM name if it's valid, otherwise use parser name
            best_name = llm_name if (llm_name and llm_name != "Name not found" and len(llm_name.split()) >= 2) else parser_name

            # Update metadata with best name
            updated_metadata = resume_data.get('metadata', {}).copy()
            updated_metadata['candidate_name'] = best_name

            # Prepare detailed results
            result = {
                'success': True,
                'filename': resume_data['filename'],
                'final_score': round(final_score, 2),
                'scores': combined_scores,
                'recommendation': recommendation,
                'analysis': improved_analysis,  # Use improved analysis
                'strengths': improved_analysis.get('strengths', []),
                'weaknesses': improved_analysis.get('weaknesses', []),
                'summary': improved_analysis.get('summary', ''),
                'processing_time': llm_result.get('processing_time', 0),
                'metadata': updated_metadata,
                'timestamp': datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error scoring resume {resume_data.get('filename', 'unknown')}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'filename': resume_data.get('filename', 'unknown'),
                'scores': {},
                'recommendation': 'REJECT'
            }
    
    def _combine_scores(self, llm_analysis: Dict[str, Any], resume_data: Dict[str, Any], 
                       job_description: str) -> Dict[str, float]:
        """Combine LLM scores with rule-based adjustments"""
        
        # Start with LLM scores
        combined_scores = {
            'skills_match': llm_analysis.get('skills_match', 50),
            'experience_relevance': llm_analysis.get('experience_relevance', 50),
            'education_match': llm_analysis.get('education_match', 50),
            'keywords_match': llm_analysis.get('keywords_match', 50),
            'overall_fit': llm_analysis.get('overall_fit', 50)
        }
        
        # Apply rule-based adjustments
        adjustments = self._calculate_rule_based_adjustments(resume_data, job_description)
        
        # Combine scores with adjustments
        for criterion, base_score in combined_scores.items():
            adjustment = adjustments.get(criterion, 0)
            adjusted_score = base_score + adjustment
            combined_scores[criterion] = max(self.min_score, min(self.max_score, adjusted_score))
        
        return combined_scores
    
    def _calculate_rule_based_adjustments(self, resume_data: Dict[str, Any], 
                                        job_description: str) -> Dict[str, float]:
        """Calculate rule-based score adjustments"""
        
        adjustments = {
            'skills_match': 0,
            'experience_relevance': 0,
            'education_match': 0,
            'keywords_match': 0,
            'overall_fit': 0
        }
        
        resume_text = resume_data.get('text', '').lower()
        job_desc_lower = job_description.lower()
        metadata = resume_data.get('metadata', {})
        
        # Keywords matching adjustment
        job_keywords = self._extract_keywords(job_desc_lower)
        keyword_matches = sum(1 for keyword in job_keywords if keyword in resume_text)
        if job_keywords:
            keyword_match_ratio = keyword_matches / len(job_keywords)
            adjustments['keywords_match'] += (keyword_match_ratio - 0.5) * 20  # ±10 points
        
        # Contact information bonus
        if metadata.get('has_email') and metadata.get('has_phone'):
            adjustments['overall_fit'] += 5
        
        # Professional profiles bonus
        if metadata.get('has_linkedin'):
            adjustments['overall_fit'] += 3
        if metadata.get('has_github'):
            adjustments['skills_match'] += 5
        
        # Resume length considerations
        word_count = metadata.get('word_count', 0)
        if word_count < 100:  # Too short
            adjustments['overall_fit'] -= 10
        elif word_count > 2000:  # Too long
            adjustments['overall_fit'] -= 5
        
        # Experience indicators
        experience_indicators = ['years', 'experience', 'worked', 'developed', 'managed', 'led']
        experience_count = sum(1 for indicator in experience_indicators if indicator in resume_text)
        if experience_count >= 3:
            adjustments['experience_relevance'] += 5
        
        return adjustments
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract important keywords from job description"""
        
        # Common technical and professional keywords
        common_keywords = [
            'python', 'java', 'javascript', 'react', 'node', 'sql', 'database',
            'aws', 'docker', 'kubernetes', 'git', 'agile', 'scrum', 'api',
            'machine learning', 'data science', 'analytics', 'cloud', 'devops',
            'frontend', 'backend', 'fullstack', 'mobile', 'web development',
            'project management', 'leadership', 'team', 'communication'
        ]
        
        # Extract keywords that appear in the job description
        found_keywords = []
        for keyword in common_keywords:
            if keyword in text:
                found_keywords.append(keyword)
        
        # Also extract words that appear multiple times (likely important)
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text)
        word_freq = {}
        for word in words:
            if word not in ['the', 'and', 'for', 'with', 'you', 'will', 'are', 'have']:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Add frequently mentioned words
        frequent_words = [word for word, freq in word_freq.items() if freq >= 2]
        found_keywords.extend(frequent_words[:10])  # Top 10 frequent words
        
        return list(set(found_keywords))  # Remove duplicates
    
    def _calculate_weighted_score(self, scores: Dict[str, float]) -> float:
        """Calculate final weighted score"""
        
        weighted_sum = 0
        total_weight = 0
        
        for criterion, score in scores.items():
            weight = self.weights.get(criterion, 0)
            weighted_sum += score * weight
            total_weight += weight
        
        if total_weight == 0:
            return 50  # Default score if no weights
        
        return weighted_sum / total_weight
    
    def _generate_recommendation(self, final_score: float, scores: Dict[str, float]) -> str:
        """Generate hiring recommendation based on scores"""
        
        if final_score >= 80:
            return "HIRE"
        elif final_score >= self.passing_score:
            # Check if any critical area is too low
            critical_areas = ['skills_match', 'experience_relevance']
            critical_scores = [scores.get(area, 0) for area in critical_areas]
            
            if any(score < 40 for score in critical_scores):
                return "CONSIDER"
            else:
                return "HIRE"
        elif final_score >= 40:
            return "CONSIDER"
        else:
            return "REJECT"
    
    def batch_score_resumes(self, resume_list: List[Dict[str, Any]], 
                           job_description: str) -> List[Dict[str, Any]]:
        """Score multiple resumes in batch"""
        
        results = []
        total_resumes = len(resume_list)
        
        logger.info(f"Starting batch scoring of {total_resumes} resumes")
        
        for i, resume_data in enumerate(resume_list, 1):
            logger.info(f"Processing resume {i}/{total_resumes}: {resume_data.get('filename', 'unknown')}")
            
            result = self.score_resume(resume_data, job_description)
            results.append(result)
            
            # Log progress
            if result['success']:
                logger.info(f"Scored: {result['filename']} - Score: {result['final_score']} - {result['recommendation']}")
            else:
                logger.error(f"Failed: {result['filename']} - {result['error']}")
        
        logger.info(f"Batch scoring completed. {len([r for r in results if r['success']])} successful, {len([r for r in results if not r['success']])} failed")

        return results

    def batch_score_resumes_threaded(self, resume_list: List[Dict[str, Any]],
                                   job_description: str,
                                   progress_callback=None) -> List[Dict[str, Any]]:
        """Score multiple resumes using multi-threaded LLM calls"""

        total_resumes = len(resume_list)
        logger.info(f"Starting threaded batch scoring of {total_resumes} resumes using {self.thread_pool_size} threads")

        # Prepare resume-job pairs for batch processing
        resume_job_pairs = [(resume['text'], job_description) for resume in resume_list]

        # Use Ollama client's batch processing
        llm_results = self.ollama_client.analyze_resumes_batch(
            resume_job_pairs,
            progress_callback=lambda current, total, result: self._handle_llm_progress(
                current, total, result, resume_list, progress_callback
            )
        )

        # Process LLM results and combine with rule-based scoring
        results = []
        for i, (resume_data, llm_result) in enumerate(zip(resume_list, llm_results)):
            if llm_result and llm_result.get('success'):
                # Improve skill matching accuracy
                improved_analysis = self._improve_skill_matching(
                    llm_result['analysis'],
                    resume_data['text'],
                    job_description
                )

                # Combine LLM analysis with rule-based scoring
                combined_scores = self._combine_scores(
                    improved_analysis,
                    resume_data,
                    job_description
                )

                # Calculate final weighted score
                final_score = self._calculate_weighted_score(combined_scores)

                # Generate recommendation
                recommendation = self._generate_recommendation(final_score, combined_scores)

                # Get the best candidate name
                llm_name = improved_analysis.get('candidate_name', '')
                parser_name = resume_data.get('metadata', {}).get('candidate_name', 'Name not found')
                best_name = llm_name if (llm_name and llm_name != "Name not found" and len(llm_name.split()) >= 2) else parser_name

                # Update metadata with best name
                updated_metadata = resume_data.get('metadata', {}).copy()
                updated_metadata['candidate_name'] = best_name

                result = {
                    'success': True,
                    'filename': resume_data['filename'],
                    'final_score': round(final_score, 2),
                    'scores': combined_scores,
                    'recommendation': recommendation,
                    'analysis': improved_analysis,  # Use improved analysis
                    'strengths': improved_analysis.get('strengths', []),
                    'weaknesses': improved_analysis.get('weaknesses', []),
                    'summary': improved_analysis.get('summary', ''),
                    'processing_time': llm_result.get('processing_time', 0),
                    'metadata': updated_metadata,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                # Handle failed LLM analysis
                result = {
                    'success': False,
                    'filename': resume_data.get('filename', 'unknown'),
                    'error': llm_result.get('error', 'LLM analysis failed') if llm_result else 'No LLM result',
                    'final_score': 0,
                    'recommendation': 'REJECT',
                    'scores': {},
                    'metadata': resume_data.get('metadata', {})
                }

            results.append(result)

        logger.info(f"Threaded batch scoring completed. {len([r for r in results if r['success']])} successful, {len([r for r in results if not r['success']])} failed")

        return results

    def _handle_llm_progress(self, current: int, total: int, llm_result: dict,
                           resume_list: List[Dict[str, Any]], progress_callback):
        """Handle progress updates from LLM batch processing"""
        if progress_callback and current <= len(resume_list):
            # Create a mock result for progress callback
            resume_data = resume_list[current - 1] if current <= len(resume_list) else {}
            mock_result = {
                'filename': resume_data.get('filename', f'resume_{current}'),
                'success': llm_result.get('success', False),
                'final_score': 0,  # Will be calculated later
                'recommendation': 'PROCESSING'
            }
            progress_callback(current, total, mock_result)

    def _improve_skill_matching(self, llm_analysis: Dict[str, Any], resume_text: str, job_description: str) -> Dict[str, Any]:
        """Improve skill matching accuracy using rule-based validation"""

        # Create a copy of the analysis to modify
        improved_analysis = llm_analysis.copy()

        # Extract skills from job description
        job_skills = self._extract_skills_from_text(job_description)

        # Extract skills from resume
        resume_skills = self._extract_skills_from_text(resume_text)

        # Find actual matching skills (case-insensitive)
        actual_matching_skills = []
        for job_skill in job_skills:
            for resume_skill in resume_skills:
                if job_skill.lower() == resume_skill.lower():
                    actual_matching_skills.append(resume_skill)
                    break

        # Find actual missing skills
        actual_missing_skills = []
        for job_skill in job_skills:
            found = False
            for resume_skill in resume_skills:
                if job_skill.lower() == resume_skill.lower():
                    found = True
                    break
            if not found:
                actual_missing_skills.append(job_skill)

        # Update the analysis with accurate skill matching
        improved_analysis['matching_skills'] = actual_matching_skills
        improved_analysis['missing_skills'] = actual_missing_skills

        # Recalculate skills match score based on actual matching
        if job_skills:
            skills_match_percentage = len(actual_matching_skills) / len(job_skills) * 100
            improved_analysis['skills_match'] = min(100, max(0, skills_match_percentage))

        return improved_analysis

    def _extract_skills_from_text(self, text: str) -> List[str]:
        """Extract technical skills from text using comprehensive skill database"""

        # Comprehensive skill database
        skill_database = [
            # Programming Languages
            'Python', 'JavaScript', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift',
            'Kotlin', 'TypeScript', 'Scala', 'R', 'MATLAB', 'Perl', 'Shell', 'Bash', 'PowerShell',

            # Web Technologies
            'HTML', 'HTML5', 'CSS', 'CSS3', 'React', 'Angular', 'Vue.js', 'Node.js', 'Express',
            'Django', 'Flask', 'Spring', 'Laravel', 'Rails', 'ASP.NET', 'jQuery', 'Bootstrap',
            'Sass', 'Less', 'Webpack', 'Vite', 'Next.js', 'Nuxt.js', 'Svelte',

            # Databases
            'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'SQLite', 'Oracle', 'SQL Server',
            'Cassandra', 'DynamoDB', 'Elasticsearch', 'Neo4j', 'InfluxDB',

            # Cloud & DevOps
            'AWS', 'Azure', 'Google Cloud', 'GCP', 'Docker', 'Kubernetes', 'Jenkins', 'GitLab CI',
            'GitHub Actions', 'Terraform', 'Ansible', 'Chef', 'Puppet', 'Vagrant', 'Helm',

            # Data Science & ML
            'TensorFlow', 'PyTorch', 'Keras', 'scikit-learn', 'Pandas', 'NumPy', 'Matplotlib',
            'Seaborn', 'Plotly', 'Jupyter', 'Apache Spark', 'Hadoop', 'Tableau', 'Power BI',

            # Mobile Development
            'React Native', 'Flutter', 'Xamarin', 'Ionic', 'Cordova', 'Android', 'iOS',

            # Tools & Others
            'Git', 'SVN', 'JIRA', 'Confluence', 'Slack', 'VS Code', 'IntelliJ', 'Eclipse',
            'Postman', 'Swagger', 'REST', 'GraphQL', 'SOAP', 'Microservices', 'API',
            'Agile', 'Scrum', 'Kanban', 'TDD', 'BDD', 'CI/CD', 'DevOps'
        ]

        # Convert text to lowercase for matching
        text_lower = text.lower()

        # Find skills that appear in the text
        found_skills = []
        for skill in skill_database:
            # Check for exact word match (case-insensitive)
            import re
            pattern = r'\b' + re.escape(skill.lower()) + r'\b'
            if re.search(pattern, text_lower):
                found_skills.append(skill)

        return found_skills


def test_scoring_engine():
    """Test function for the scoring engine"""
    
    # Sample resume data
    sample_resume = {
        'success': True,
        'filename': 'john_doe.txt',
        'text': """John Doe
        Software Developer
        <EMAIL>
        (*************
        LinkedIn: linkedin.com/in/johndoe
        GitHub: github.com/johndoe
        
        Experience:
        - 3 years Python development
        - React and JavaScript experience
        - SQL database management
        - AWS cloud services
        - Agile development
        
        Education:
        Bachelor's in Computer Science
        """,
        'metadata': {
            'word_count': 50,
            'has_email': True,
            'has_phone': True,
            'has_linkedin': True,
            'has_github': True
        }
    }
    
    sample_job_desc = """
    Software Developer Position
    Required Skills: Python, JavaScript, React, SQL, AWS
    Experience: 2-5 years in software development
    Education: Bachelor's degree in Computer Science preferred
    Must have experience with Agile methodologies
    """
    
    # Test scoring
    engine = ScoringEngine()
    result = engine.score_resume(sample_resume, sample_job_desc)
    
    print("Scoring Test Result:")
    print(f"Final Score: {result.get('final_score', 'N/A')}")
    print(f"Recommendation: {result.get('recommendation', 'N/A')}")
    print(f"Success: {result.get('success', False)}")


if __name__ == "__main__":
    test_scoring_engine()
