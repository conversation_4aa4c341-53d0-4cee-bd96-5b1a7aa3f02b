{"meta": {"version": "1.0", "exported_at": "2025-07-08T22:51:31.249833", "total_candidates": 2, "successful_analyses": 2, "failed_analyses": 0, "job_description": "\nSenior Software Developer Position - TechCorp Solutions\n\nPOSITION OVERVIEW:\nWe are seeking a highly skilled Senior Software Developer to join our dynamic engineering team. The ideal candidate will have strong technical expertise, excellent problem-solving abilities, and a passion for building scalable software solutions.\n\nREQUIRED TECHNICAL SKILLS:\n- Programming Languages: Python (3+ years), JavaScript (ES6+), TypeScript\n- Web Frameworks: React.js, Node.js, Express.js, Django/Flask\n- Database Technologies: PostgreSQL, MongoDB, Redis\n- Cloud Platforms: AWS (EC2, S3, RDS, Lambda), Docker containerization\n- Version Control: Git, GitHub/GitLab workflows\n- API Development: RESTful APIs, GraphQL\n- Testing: Unit testing, Integration testing, TDD practices\n\nREQUIRED EXPERIENCE:\n- 3-5 years of professional software development experience\n- Experience with Agile/Scrum development methodologies\n- Proven track record of delivering production-ready applications\n- Experience with CI/CD pipelines and DevOps practices\n- Strong understanding of software architecture patterns\n- Experience with code reviews and mentoring junior developers\n\nREQUIRED EDUCATION & CERTIFICATIONS:\n- Bachelor's degree in Computer Science, Software Engineering, or related technical field\n- Relevant certifications (AWS, Azure, or Google Cloud) preferred\n\nPREFERRED QUALIFICATIONS:\n- Master's degree in Computer Science or related field\n- Experience with microservices architecture\n- Knowledge of machine learning frameworks (TensorFlow, PyTorch)\n- Experience with mobile development (React Native, Flutter)\n- Familiarity with blockchain technologies\n- Open source contributions\n\nSOFT SKILLS:\n- Excellent communication and collaboration skills\n- Strong analytical and problem-solving abilities\n- Ability to work independently and in team environments\n- Leadership potential and mentoring capabilities\n- Adaptability to new technologies and frameworks\n\nRESPONSIBILITIES:\n- Design, develop, and maintain scalable web applications\n- Collaborate with cross-functional teams (Product, Design, QA)\n- Participate in architectural decisions and technical planning\n- Conduct code reviews and ensure code quality standards\n- Troubleshoot and resolve complex technical issues\n- Mentor junior developers and contribute to team knowledge sharing\n- Stay updated with emerging technologies and industry best practices\n\nCOMPANY BENEFITS:\n- Competitive salary range: $90,000 - $130,000\n- Comprehensive health, dental, and vision insurance\n- 401(k) with company matching\n- Flexible work arrangements (hybrid/remote options)\n- Professional development budget ($2,000 annually)\n- Stock options and performance bonuses\n- 25 days PTO + holidays\n", "processed_at": "2025-07-08T22:51:31.247831"}, "summary_statistics": {"average_score": 12.91, "score_distribution": {"excellent": 0, "good": 0, "average": 0, "below_average": 2}, "recommendations": {"HIRE": 0, "CONSIDER": 0, "REJECT": 2}, "processing_time": 7.53}, "candidates": [{"id": "resume_1", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 1.55, "skills_match": 0, "experience_score": 5, "education_score": 0, "keywords_match": 0, "overall_fit": 3, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant technical skills and experience for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "TypeScript", "React", "Node.js", "Express", "Django", "Flask", "PostgreSQL", "MongoDB", "Redis", "AWS", "Azure", "Google Cloud", "<PERSON>er", "TensorFlow", "PyTorch", "React Native", "Flutter", "Git", "GraphQL", "Microservices", "API", "Agile", "Scrum", "TDD", "CI/CD", "DevOps"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.387444019317627, "processed_at": "2025-07-08T22:51:31.238832", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}, {"id": "resume_2", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 24.26, "skills_match": 8.571428571428571, "experience_score": 40.0, "education_score": 10.0, "keywords_match": 23.448275862068968, "overall_fit": 55.0, "growth_potential": 30.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate lacks relevant technical skills and experience for the position.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python"], "missing_skills": ["JavaScript", "TypeScript", "React", "Node.js", "Express", "Django", "Flask", "PostgreSQL", "MongoDB", "Redis", "AWS", "Azure", "Google Cloud", "<PERSON>er", "TensorFlow", "PyTorch", "React Native", "Flutter", "Git", "GraphQL", "Microservices", "API", "Agile", "Scrum", "TDD", "CI/CD", "DevOps"], "skill_match_percentage": 3.6}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["3-5 years of professional software development experience"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology"], "education_level": "BASIC"}, "assessment": {"strengths": ["Positive attitude", "Willingness and motivation to quickly learn new programs"], "weaknesses": ["Lack of relevant technical skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of relevant technical skills", "Insufficient experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 7.464153051376343, "processed_at": "2025-07-08T22:51:31.247831", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel."}], "top_candidates": [{"id": "resume_2", "filename": "resume.pdf", "candidate_name": "AKURATHI SASIDHAR", "scores": {"final_score": 24.26, "skills_match": 8.571428571428571, "experience_score": 40.0, "education_score": 10.0, "keywords_match": 23.448275862068968, "overall_fit": 55.0, "growth_potential": 30.0}, "recommendation": {"decision": "REJECT", "reason": "Candidate lacks relevant technical skills and experience for the position.", "confidence": "LOW"}, "skills_analysis": {"matching_skills": ["Python"], "missing_skills": ["JavaScript", "TypeScript", "React", "Node.js", "Express", "Django", "Flask", "PostgreSQL", "MongoDB", "Redis", "AWS", "Azure", "Google Cloud", "<PERSON>er", "TensorFlow", "PyTorch", "React Native", "Flutter", "Git", "GraphQL", "Microservices", "API", "Agile", "Scrum", "TDD", "CI/CD", "DevOps"], "skill_match_percentage": 3.6}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["3-5 years of professional software development experience"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": ["Bachelor Of Technology"], "education_level": "BASIC"}, "assessment": {"strengths": ["Positive attitude", "Willingness and motivation to quickly learn new programs"], "weaknesses": ["Lack of relevant technical skills"], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": ["Lack of relevant technical skills", "Insufficient experience"], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 7.464153051376343, "processed_at": "2025-07-08T22:51:31.247831", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\resume.pdf", "file_size": 0, "word_count": 267, "success": true, "error": null}, "summary": "Dedicated and passionate coder with a strong desire to excel."}, {"id": "resume_1", "filename": "Profile_1.pdf", "candidate_name": "<PERSON><PERSON><PERSON>", "scores": {"final_score": 1.55, "skills_match": 0, "experience_score": 5, "education_score": 0, "keywords_match": 0, "overall_fit": 3, "growth_potential": 0}, "recommendation": {"decision": "REJECT", "reason": "Lack of relevant technical skills and experience for the position", "confidence": "LOW"}, "skills_analysis": {"matching_skills": [], "missing_skills": ["Python", "JavaScript", "TypeScript", "React", "Node.js", "Express", "Django", "Flask", "PostgreSQL", "MongoDB", "Redis", "AWS", "Azure", "Google Cloud", "<PERSON>er", "TensorFlow", "PyTorch", "React Native", "Flutter", "Git", "GraphQL", "Microservices", "API", "Agile", "Scrum", "TDD", "CI/CD", "DevOps"], "skill_match_percentage": 0.0}, "experience_analysis": {"matching_experience": [], "experience_gaps": ["Required experience not found in resume"], "experience_level": "JUNIOR"}, "education_analysis": {"education_highlights": [], "education_level": "BASIC"}, "assessment": {"strengths": [], "weaknesses": [], "red_flags": [], "cultural_fit_indicators": []}, "hiring_insights": {"salary_expectation_alignment": "LOW", "interview_focus_areas": [], "onboarding_priority": "LOW"}, "metadata": {"processing_time": 5.387444019317627, "processed_at": "2025-07-08T22:51:31.238832", "file_path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Test Resume Parser\\resumes\\Profile_1.pdf", "file_size": 0, "word_count": 617, "success": true, "error": null}, "summary": ""}]}