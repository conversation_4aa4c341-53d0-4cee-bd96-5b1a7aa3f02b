filename,candidate_name,final_score,recommendation,recommendation_reason,skills_match_score,experience_score,education_score,keywords_match_score,overall_fit_score,growth_potential_score,matching_skills,missing_skills,matching_experience,experience_gaps,education_highlights,strengths,weaknesses,red_flags,cultural_fit_indicators,salary_expectation_alignment,interview_focus_areas,summary,processing_time,success,error,file_path
Profile_1.pdf,<PERSON><PERSON><PERSON>,59.69,CO<PERSON><PERSON><PERSON>,Candidate lacks relevant technical expertise and experience for the Senior Software Developer position.,80.0,45.0,20.0,80.6896551724138,53.0,70.0,Python; JavaScript; React.js; Node.js; Django/Flask,AWS; EC2; S3; RDS; Lambda; GitLab workflows; GraphQL; Unit testing; Integration testing; TDD practices; Microservices architecture; Machine learning frameworks; Mobile development; Blockchain technologies; Open source contributions,Agile/Scrum development methodologies; CI/CD pipelines and DevOps practices; Code reviews and mentoring junior developers,"Cloud Platforms: Docker containerization; API Development: RESTful APIs; Testing: Unit testing, Integration testing, TDD practices",,Excellent communication and collaboration skills; Strong analytical and problem-solving abilities; Ability to work independently and in team environments; Leadership potential and mentoring capabilities; Adaptability to new technologies and frameworks,Lack of technical expertise in specific areas; Limited experience with emerging technologies,,,LOW,,"Summary I help founders, CEOs, and coaches to build their strong brand. I have worked with 15+ founders, CEOs, and coaches.",9.770732641220093,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_1.pdf
