<PERSON>
Frontend Developer
Email: <EMAIL>
Phone: (*************
Portfolio: bobwilson.dev

EXPERIENCE:
Frontend Developer | WebDesign Studio | 2022-2024
- Developed responsive web applications using React and TypeScript
- Implemented modern UI/UX designs with CSS3 and Sass
- Optimized website performance and accessibility
- Collaborated with backend developers for API integration
- Used Git for version control and agile development practices

Junior Web Developer | Creative Agency | 2021-2022
- Built websites using HTML5, CSS3, and JavaScript
- Worked with content management systems (WordPress)
- Assisted in mobile-first responsive design implementation
- Participated in code reviews and team meetings

EDUCATION:
Associate Degree in Web Development
Community College | 2019-2021

SKILLS:
Frontend: HTML5, CSS3, JavaScript, TypeScript, React, Vue.js
Styling: Sass, Less, Bootstrap, Tailwind CSS
Tools: Webpack, Vite, npm, Git, VS Code
Design: Figma, Adobe XD, Photoshop
Testing: Jest, Cypress, React Testing Library

PROJECTS:
- E-commerce Website with React and <PERSON>ux
- Portfolio Website with Next.js
- Mobile App UI with React Native