filename,candidate_name,final_score,recommendation,recommendation_reason,skills_match_score,experience_score,education_score,keywords_match_score,overall_fit_score,growth_potential_score,matching_skills,missing_skills,matching_experience,experience_gaps,education_highlights,strengths,weaknesses,red_flags,cultural_fit_indicators,salary_expectation_alignment,interview_focus_areas,summary,processing_time,success,error,file_path
Profile_1.pdf,<PERSON><PERSON><PERSON>,32.82,REJECT,Not suitable for the role due to lack of relevant experience and skills.,0,45.0,0,71.33333333333333,73.0,80.0,,Python; MySQL; Git,,"Solid understanding of Python fundamentals including lists, dictionaries, sets, tuples, and OOP concepts",,Good problem-solving and algorithmic thinking,Lack of experience in backend development and data-driven applications,,,LOW,,,5.689244985580444,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_1.pdf
resume.pdf,AKURATHI SASIDHAR,47.6,CONS<PERSON><PERSON>,<PERSON><PERSON> required technical skills for the position,71.66666666666666,30.0,20.0,48.0,60.0,70.0,Python; MySQL,Git,,Required experience not found in resume,Bachelor Of Technology,Dedicated and passionate coder; Technologically adept,Gaps in technical skills,,,LOW,Technical skills; Problem-solving abilities,Dedicated and passionate coder with a strong desire to excel,5.833555459976196,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\resume.pdf
