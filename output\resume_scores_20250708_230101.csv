filename,candidate_name,final_score,recommendation,recommendation_reason,skills_match_score,experience_score,education_score,keywords_match_score,overall_fit_score,growth_potential_score,matching_skills,missing_skills,matching_experience,experience_gaps,education_highlights,strengths,weaknesses,red_flags,cultural_fit_indicators,salary_expectation_alignment,interview_focus_areas,summary,processing_time,success,error,file_path
Profile_10.pdf,<PERSON><PERSON><PERSON>,59.35,<PERSON><PERSON><PERSON><PERSON>,"Strong experience in backend development and problem-solving, but limited exposure to full-stack development.",5,95.0,60.0,83.0,85.0,95.0,,Python; MySQL; Git,Backend development; Data-driven applications,,"NIT, Nagpur",Problem-solving; Algorithmic thinking,,,Collaborative work culture,LOW,Full-stack development; Collaborative work culture,Experienced software engineer with a strong background in backend development and problem-solving.,11.853647708892822,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_10.pdf
Profile_12.pdf,<PERSON><PERSON>,45.87,<PERSON><PERSON><PERSON><PERSON>,"Lacks relevant experience for the role, but shows potential with good problem-solving skills.",0,80.0,60.0,45.333333333333336,78.0,60.0,,Python; MySQL; Git,,Solid understanding of Python fundamentals,"Masters degree, Computer Science",Good problem-solving and algorithmic thinking,Lack of experience in backend development and data-driven applications,,,LOW,Backend development; Data-driven applications,Software development manager with 6 years of experience in Amazon.,12.616151571273804,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_12.pdf
Profile_11.pdf,Aishwarya Mahapatra,57.92,CONSIDER,Lack of required skills and experience for the role.,71.66666666666666,85.0,0,39.333333333333336,73.0,80.0,Python; MySQL,Git,,Exposure to version control systems like Git,,,,,,LOW,,,17.168038845062256,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_11.pdf
Profile_1.pdf,Preksha Jain,11.82,REJECT,Lack of relevant experience and skills for the job requirements,0,25.0,0,1.3333333333333321,53.0,40.0,,Python; MySQL; Git,,CRUD operations; Deployment pipelines,,,,,,LOW,,,6.292381763458252,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_1.pdf
Profile_17.pdf,Dharinee Gupta,31.57,REJECT,Lack of relevant experience and skills for the role,0,80.0,0,31.333333333333332,53.0,70.0,,Python; MySQL; Git,9 years experience in software engineering,,,Strong problem-solving skills,Limited experience in backend development,,,LOW,Backend development; Python fundamentals,Experienced software engineer with strong problem-solving skills,6.271728992462158,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_17.pdf
Profile_13.pdf,Raghu Uppalli,46.38,CONSIDER,"Relevant experience and skills, but some missing skills",0,85.0,0,86.66666666666667,78.0,85.0,,Python; MySQL; Git,Network path discovery and analysis; Link inference in large networks based on incomplete data,,,"Data driven, lean startup practitioner",,,Not mentioned,LOW,Network path discovery; Link inference,"Entrepreneur, product and engineering manager with over 10 years of management experience and 17 years of development experience",12.515007734298706,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_13.pdf
Profile_14.pdf,Vijay Komarapu,29.07,REJECT,Candidate has some relevant training expertise but lacks direct experience in backend development and data-driven applications.,0,70.0,0,31.333333333333332,53.0,80.0,,Python; MySQL; Git,NLP practitioner; Corporate training experience,No direct experience in backend development or data-driven applications,M-Technology Business Development Manager,Training expertise; Project management skills,Lack of relevant work experience in Python and MySQL,,"Friendly, collaborative work culture",LOW,Python fundamentals; MySQL queries,Experienced HR recruitment specialist with training expertise seeking a role in backend development and data-driven applications.,17.247315168380737,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_14.pdf
Profile_16.pdf,Bill Allocca,38.57,REJECT,Overqualified for entry-level position,0,80.0,20.0,41.33333333333333,73.0,80.0,,Python; MySQL; Git,Software Development; CTO; Director of Software,,"Harvard University A.B., Computer Science",Leadership experience; Technical expertise,Lack of recent experience in backend development,,Strong work ethic; Collaborative mindset,LOW,Transitioning to backend development; Leadership style,Highly experienced software engineer with strong technical skills and leadership experience.,13.14304256439209,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_16.pdf
Profile_15.pdf,Suyash Gaidhani,50.82,CONSIDER,"Relevant experience, but lack of fit for specific skills required.",0,85.0,90.0,41.33333333333333,78.0,80.0,,Python; MySQL; Git,Enterprise Integration Architecture; SOA and Microservices,,B.E. (Electronics Telecommunications),Customer focused individual; Strong understanding of Core Java,Lack of experience in backend development and data-driven applications,,,LOW,Python data structures; MySQL queries,Customer focused individual with extensive software development experience.,16.92365336418152,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_15.pdf
Profile_18.pdf,Kartick Kumar Nayagam,4.3,REJECT,Candidate lacks relevant skills and experience for the role,0,10.0,0,0,18.0,30.0,,Python; MySQL; Git,,No experience in backend development or data-driven applications,Bachelor of Engineering,Senior Software Development Manager experience,Lack of relevant work experience for the role,,,LOW,Python fundamentals; MySQL queries,"Senior Software Development Manager with 6 years of experience in Amazon, but no relevant work experience for Python & MySQL Developer role",13.87079405784607,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_18.pdf
Profile_19.pdf,Arindam Mukherjee,20.85,REJECT,Lack of relevant experience and skills for the role,0,25.0,0,44.0,58.0,70.0,,Python; MySQL; Git,,Required experience not found in resume,,"Proficient in building distributed, cloud native and server-side applications",Gaps in backend development and data-driven applications skills,,,LOW,Backend development; Data-driven applications,"Programmer, architect and technical lead with expertise in C++, Golang, Rust, Linux.",16.9538676738739,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_19.pdf
Profile_20.pdf,Nivetha S,38.8,REJECT,"Relevant HR experience, but lacks backend development skills",0,80.0,0,60.0,68.0,50.0,,Python; MySQL; Git,,No experience in backend development or data-driven applications,,HR professional experience; E2E Recruitment skills,Lack of relevant work experience,,,LOW,Backend development skills; Data-driven applications,HR professional with 2.5 years of experience in IT and non-IT industry,13.600553512573242,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_20.pdf
Profile_21.pdf,Kaviya E.,31.3,REJECT,Candidate has relevant HR experience but lacks backend development skills.,0,80.0,0,30.0,53.0,70.0,,Python; MySQL; Git,IT hiring experience; Recruiting strategies implementation,,Easwari Engineering,Talent Acquisition expertise; Head hunting experience,Lack of backend development experience,,Collaborative work culture,LOW,Backend development skills; Recruiting strategies,HR Talent Acquisition professional with IT hiring experience and recruiting strategies expertise.,16.660176753997803,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_21.pdf
Profile_2.pdf,Vasu Beri,46.13,CONSIDER,"Lacks required skills for the position, but has potential with growth support.",0,80.0,60.0,46.666666666666664,78.0,80.0,,Python; MySQL; Git,React Js Developer; Software Engineer Intern,,"Bachelor of Technology - BTech, Computer Science",Problem-solving skills; Algorithmic thinking,Limited experience in backend development,,Friendly work culture,LOW,Python fundamentals; MySQL queries,Freshers with Python and MySQL experience seeking backend development opportunities.,13.611565589904785,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_2.pdf
Profile_3.pdf,Fahad Ibraheem,8.83,REJECT,Lack of required technical skills,0,20.0,0,2.666666666666666,33.0,50.0,,Python; MySQL; Git,,Required experience not found in resume,,,Gaps in relevant technical skills,,,LOW,,,15.636712312698364,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_3.pdf
Profile_4.pdf,Deepa Sajjanshetty,19.08,REJECT,Limited relevant experience for the role,0,45.0,0,12.666666666666666,53.0,70.0,,Python; MySQL; Git,,Backend development experience,,Goal-oriented professional; Proactive,Lack of backend development experience,,,LOW,Backend development experience; Python and MySQL skills,,12.428304195404053,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_4.pdf
Profile_6.pdf,Arsh Goyal,51.13,CONSIDER,"Strong leadership skills and AI expertise, but limited backend development experience.",0,90.0,70.0,46.666666666666664,88.0,95.0,,Python; MySQL; Git,Software Solutions; Technical Advocacy; Generative AI,,NIT Jalandhar; Unacademy,Leadership experience; AI expertise,Limited backend development experience,,Collaborative work culture,MEDIUM,Leadership experience; AI expertise,"Senior Software Engineer with 10+ years of experience in software solutions, technical advocacy, and generative AI.",15.806605577468872,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_6.pdf
Profile_5.pdf,Sanika Jain,55.8,CONSIDER,"Strong experience with Apache Kafka and Kafka Streams, but lacking in some fundamental Python skills.",0,90.0,60.0,79.0,85.0,95.0,,Python; MySQL; Git,,,B.Tech in Electronics Engineering from IIT BHU,Robust problem-solving; High-volume data workloads management,,,,MEDIUM,Python fundamentals; MySQL queries,"Transformative journey as a Member of Technical Staff at Oracle, with expertise in data processing projects.",12.48155426979065,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_5.pdf
Profile_7.pdf,Priyansh Agarwal,45.08,CONSIDER,"Strong foundation in data structures and algorithms, but limited experience in backend development.",0,95.0,0,62.666666666666664,88.0,95.0,,Python; MySQL; Git,,,,Strong foundation in data structures and algorithms; Passionate competitive programmer,,,,LOW,Data structures and algorithms; Backend development,Passionate competitive programmer with a strong foundation in data structures and algorithms.,15.672299146652222,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_7.pdf
Profile_9.pdf,Top Skills,50.65,CONSIDER,"Relevant experience and skills, but limited fit for the role.",33.33333333333333,85.0,0,58.0,78.0,90.0,Python,MySQL; Git,Ex- Microsoft; Ex- Amazon,,,Innovation; Scalability,Lack of experience in backend development,,,LOW,Backend development; Python fundamentals,Experienced software engineer with a strong background in Android and distributed systems.,13.457470178604126,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_9.pdf
Profile_8.pdf,Riddhi Dutta,34.88,REJECT,Lacks relevant skills and experience for the role.,0,45.0,20.0,76.66666666666667,53.0,70.0,,Python; MySQL; Git,,Version control systems like Git,Merit Certificate for Academic Excellence in Computer Science,Good problem-solving skills,Lack of experience with backend development,,,LOW,Python fundamentals; MySQL queries,Freshers with strong programming skills and good problem-solving abilities.,16.319137573242188,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_8.pdf
resume.pdf,AKURATHI SASIDHAR,58.1,CONSIDER,Lack of direct experience with MySQL queries and version control systems.,71.66666666666666,50.0,30.0,68.0,60.0,65.0,Python; MySQL,Git,,,Bachelor Of Technology; CGPA: 8.11,,,,,LOW,,Dedicated and passionate coder with a strong desire to excel.,12.15880274772644,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\resume.pdf
