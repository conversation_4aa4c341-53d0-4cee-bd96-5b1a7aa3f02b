<PERSON> Doe
Software Developer
Email: <EMAIL>
Phone: (*************
LinkedIn: linkedin.com/in/johndoe
GitHub: github.com/johndoe

EXPERIENCE:
Senior Software Developer | TechCorp Inc. | 2021-2024
- Developed web applications using Python, Django, and React
- Implemented RESTful APIs and microservices architecture
- Worked with PostgreSQL databases and AWS cloud services
- Led a team of 3 junior developers in agile environment
- Improved application performance by 40% through optimization

Software Developer | StartupXYZ | 2019-2021
- Built full-stack applications with JavaScript, Node.js, and React
- Integrated third-party APIs and payment systems
- Collaborated with cross-functional teams using Scrum methodology
- Deployed applications using Docker and Kubernetes

EDUCATION:
Bachelor of Science in Computer Science
University of Technology | 2015-2019
GPA: 3.8/4.0

SKILLS:
Programming Languages: Python, JavaScript, Java, SQL
Frameworks: Django, React, Node.js, Express
Databases: PostgreSQL, MongoDB, Redis
Cloud: AWS (EC2, S3, RDS), <PERSON><PERSON>, Kubernetes
Tools: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, VS Code

CERTIFICATIONS:
- AWS Certified Developer Associate (2023)
- Certified Scrum Master (2022)