filename,candidate_name,final_score,recommendation,recommendation_reason,skills_match_score,experience_score,education_score,keywords_match_score,overall_fit_score,growth_potential_score,matching_skills,missing_skills,matching_experience,experience_gaps,education_highlights,strengths,weaknesses,red_flags,cultural_fit_indicators,salary_expectation_alignment,interview_focus_areas,summary,processing_time,success,error,file_path
Profile_1.pdf,Name not found,37.69,REJECT,"While <PERSON><PERSON><PERSON> has a strong background in personal branding and marketing, her technical expertise does not align with the job requirements. Her career path is unconventional, and she lacks experience in areas such as microservices architecture, machine learning frameworks, and mobile development. However, she demonstrates excellent communication skills, analytical abilities, and adaptability to new technologies, which are valuable assets for a software developer role.",40.0,35.0,20.0,40.689655172413794,58.0,70.0,Python; JavaScript; React.js; Node.js; Django/Flask,"TypeScript (less than 3 years); PostgreSQL (less than 1 year); AWS (EC2, S3, RDS, Lambda) (less than 1 year); Git (less than 5 years); GraphQL; TDD practices (less than 3 years)",Experience with Agile/Scrum development methodologies; Proven track record of delivering production-ready applications,CI/CD pipelines and DevOps practices (less than 1 year); Strong understanding of software architecture patterns (less than 2 years); Code reviews and mentoring junior developers (less than 1 year),"Bachelor's degree in Computer Science; Relevant certifications (AWS, Azure, or Google Cloud)",Excellent communication skills; Strong analytical abilities; Ability to work independently and in team environments,Lack of experience with microservices architecture; Limited knowledge of machine learning frameworks; No experience with mobile development,Unconventional career path (Personal Branding Strategist); Limited technical expertise compared to the job requirements,Adaptability to new technologies and frameworks; Leadership potential and mentoring capabilities,LOW,Technical skills alignment; Career path and unconventional experience; Leadership and collaboration indicators,"Preksha has a unique background in personal branding and marketing, but her technical expertise does not meet the job requirements. She lacks experience in areas such as microservices architecture, machine learning frameworks, and mobile development, but demonstrates strong communication skills, analytical abilities, and adaptability to new technologies.",10.979550838470459,True,,C:\Users\<USER>\OneDrive\Desktop\Test Resume Parser\resumes\Profile_1.pdf
