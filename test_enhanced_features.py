#!/usr/bin/env python3
"""
Test script for enhanced resume parser features
Tests the new columns and professional prompts
"""

import json
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from resume_parser import ResumeParser
from ollama_client import OllamaClient
from scoring_engine import ScoringEngine
from export_utils import ExportUtils
from config import DEFAULT_JOB_DESCRIPTION

def test_enhanced_analysis():
    """Test the enhanced analysis with new columns"""
    
    print("🧪 Testing Enhanced Resume Analysis Features")
    print("=" * 60)
    
    # Sample resume for testing
    sample_resume_text = """
    <PERSON>
    Senior Software Developer
    Email: <EMAIL>
    Phone: (*************
    LinkedIn: linkedin.com/in/johnsmith
    GitHub: github.com/johnsmith
    
    EXPERIENCE:
    Senior Software Developer | TechCorp Inc. | 2021-2024
    - Developed scalable web applications using Python, Django, and React
    - Led a team of 5 developers in agile environment
    - Implemented CI/CD pipelines using Jenkins and Docker
    - Worked with AWS services (EC2, S3, RDS, Lambda)
    - Improved application performance by 40% through optimization
    - Mentored junior developers and conducted code reviews
    
    Software Developer | StartupXYZ | 2019-2021
    - Built full-stack applications with JavaScript, Node.js, and React
    - Integrated third-party APIs and payment systems
    - Collaborated with cross-functional teams using Scrum methodology
    - Deployed applications using Docker and Kubernetes
    
    EDUCATION:
    Master of Science in Computer Science
    University of Technology | 2017-2019
    
    Bachelor of Science in Computer Science
    Tech University | 2013-2017
    GPA: 3.8/4.0
    
    SKILLS:
    Programming Languages: Python, JavaScript, TypeScript, Java, SQL
    Frameworks: Django, React, Node.js, Express, Flask
    Databases: PostgreSQL, MongoDB, Redis, MySQL
    Cloud: AWS (EC2, S3, RDS, Lambda), Docker, Kubernetes
    Tools: Git, Jenkins, JIRA, VS Code, Postman
    Methodologies: Agile, Scrum, TDD, CI/CD
    
    CERTIFICATIONS:
    - AWS Certified Solutions Architect (2023)
    - Certified Scrum Master (2022)
    - Docker Certified Associate (2021)
    
    ACHIEVEMENTS:
    - Led digital transformation project saving company $500K annually
    - Increased team productivity by 35% through process optimization
    - Successfully managed remote teams across different time zones
    - Open source contributor with 50+ GitHub repositories
    """
    
    # Create mock resume data
    resume_data = {
        'success': True,
        'filename': 'john_smith_senior_dev.txt',
        'text': sample_resume_text,
        'metadata': {
            'word_count': 250,
            'has_email': True,
            'has_phone': True,
            'has_linkedin': True,
            'has_github': True,
            'potential_name': 'John Smith'
        },
        'file_size': 2048,
        'file_type': '.txt'
    }
    
    print("📄 Sample Resume Created")
    print(f"   Candidate: John Smith")
    print(f"   Word Count: {resume_data['metadata']['word_count']}")
    print(f"   File Type: {resume_data['file_type']}")
    
    # Test Ollama client with enhanced prompts
    print("\n🤖 Testing Ollama Client with Enhanced Prompts...")
    try:
        ollama_client = OllamaClient()
        
        # Test analysis
        analysis_result = ollama_client.analyze_resume(
            resume_data['text'], 
            DEFAULT_JOB_DESCRIPTION
        )
        
        if analysis_result['success']:
            print("✅ Ollama analysis successful")
            analysis = analysis_result['analysis']
            
            # Display new enhanced fields
            print(f"   Overall Score: {analysis.get('overall_score', 'N/A')}")
            print(f"   Growth Potential: {analysis.get('growth_potential', 'N/A')}")
            print(f"   Salary Alignment: {analysis.get('salary_expectation_alignment', 'N/A')}")
            
            print(f"\n📊 Enhanced Analysis Fields:")
            print(f"   Matching Skills: {len(analysis.get('matching_skills', []))} found")
            print(f"   Missing Skills: {len(analysis.get('missing_skills', []))} identified")
            print(f"   Experience Matches: {len(analysis.get('matching_experience', []))} found")
            print(f"   Experience Gaps: {len(analysis.get('experience_gaps', []))} identified")
            print(f"   Red Flags: {len(analysis.get('red_flags', []))} found")
            print(f"   Interview Focus Areas: {len(analysis.get('interview_focus_areas', []))} suggested")
            
            # Show some sample data
            if analysis.get('matching_skills'):
                print(f"\n🎯 Sample Matching Skills:")
                for skill in analysis['matching_skills'][:3]:
                    print(f"   • {skill}")
            
            if analysis.get('missing_skills'):
                print(f"\n❌ Sample Missing Skills:")
                for skill in analysis['missing_skills'][:3]:
                    print(f"   • {skill}")
                    
        else:
            print(f"❌ Ollama analysis failed: {analysis_result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Ollama client test failed: {str(e)}")
        return False
    
    # Test scoring engine with enhanced features
    print("\n⚡ Testing Enhanced Scoring Engine...")
    try:
        scoring_engine = ScoringEngine()
        scoring_result = scoring_engine.score_resume(resume_data, DEFAULT_JOB_DESCRIPTION)
        
        if scoring_result['success']:
            print("✅ Scoring engine successful")
            print(f"   Final Score: {scoring_result['final_score']}")
            print(f"   Recommendation: {scoring_result['recommendation']}")
            print(f"   Processing Time: {scoring_result['processing_time']:.2f}s")
            
            # Check if enhanced analysis is included
            if 'analysis' in scoring_result:
                print("✅ Enhanced analysis data included in results")
            else:
                print("❌ Enhanced analysis data missing")
                
        else:
            print(f"❌ Scoring failed: {scoring_result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Scoring engine test failed: {str(e)}")
        return False
    
    # Test export with enhanced columns
    print("\n📊 Testing Enhanced Export Functionality...")
    try:
        # Create sample results data
        results_data = {
            'success': True,
            'results': [scoring_result],
            'statistics': {
                'total_resumes': 1,
                'successful': 1,
                'failed': 0,
                'average_score': scoring_result['final_score'],
                'processing_time': scoring_result['processing_time'],
                'recommendations': {scoring_result['recommendation']: 1}
            },
            'job_description': DEFAULT_JOB_DESCRIPTION,
            'processed_at': '2024-01-01T12:00:00'
        }
        
        exporter = ExportUtils()
        
        # Test CSV export with enhanced columns
        csv_result = exporter.export_results(results_data, 'csv', 'test_enhanced_export.csv')
        if csv_result['success']:
            print("✅ Enhanced CSV export successful")
            print(f"   File: {csv_result['file_path']}")
            
            # Check if file contains enhanced columns
            with open(csv_result['file_path'], 'r') as f:
                header = f.readline()
                enhanced_columns = [
                    'matching_skills', 'missing_skills', 'matching_experience',
                    'experience_gaps', 'red_flags', 'growth_potential',
                    'salary_expectation_alignment', 'interview_focus_areas'
                ]
                
                found_columns = sum(1 for col in enhanced_columns if col in header)
                print(f"   Enhanced columns found: {found_columns}/{len(enhanced_columns)}")
                
        else:
            print(f"❌ CSV export failed: {csv_result['error']}")
            
        # Test Excel export
        excel_result = exporter.export_results(results_data, 'xlsx', 'test_enhanced_export.xlsx')
        if excel_result['success']:
            print("✅ Enhanced Excel export successful")
            print(f"   File: {excel_result['file_path']}")
        else:
            print(f"❌ Excel export failed: {excel_result['error']}")
            
    except Exception as e:
        print(f"❌ Export test failed: {str(e)}")
        return False
    
    print("\n🎉 All Enhanced Features Tests Completed Successfully!")
    return True

def display_sample_analysis():
    """Display a sample of what the enhanced analysis looks like"""
    
    sample_analysis = {
        "overall_score": 87,
        "skills_match": 92,
        "experience_relevance": 88,
        "education_match": 85,
        "keywords_match": 90,
        "overall_fit": 84,
        "growth_potential": 89,
        "matching_skills": ["Python", "JavaScript", "React", "Node.js", "AWS", "Docker", "PostgreSQL"],
        "missing_skills": ["GraphQL", "Kubernetes advanced features", "Machine Learning"],
        "matching_experience": ["Team leadership", "Agile development", "CI/CD implementation", "Performance optimization"],
        "experience_gaps": ["Enterprise architecture", "Security implementation"],
        "education_highlights": ["Master's in Computer Science", "AWS Certification", "Scrum Master Certification"],
        "strengths": ["Strong technical leadership", "Proven track record of optimization", "Excellent certifications"],
        "weaknesses": ["Limited ML experience", "No enterprise architecture background"],
        "red_flags": [],
        "cultural_fit_indicators": ["Team collaboration", "Mentoring experience", "Open source contributions"],
        "salary_expectation_alignment": "HIGH",
        "recommendation": "HIRE",
        "recommendation_reason": "Excellent candidate with strong technical skills, leadership experience, and relevant certifications. Minor gaps in ML and enterprise architecture can be addressed through training.",
        "summary": "John Smith is a highly qualified Senior Software Developer with 5+ years of experience, strong technical skills matching job requirements, and proven leadership abilities. His experience with modern tech stack, cloud platforms, and team management makes him an excellent fit for the role.",
        "interview_focus_areas": ["Technical architecture decisions", "Team leadership examples", "Scalability challenges"]
    }
    
    print("\n📋 SAMPLE ENHANCED ANALYSIS OUTPUT:")
    print("=" * 50)
    print(json.dumps(sample_analysis, indent=2))

if __name__ == "__main__":
    print("🚀 Enhanced Resume Parser Feature Test")
    print("This test validates the new professional features and columns\n")
    
    # Display sample analysis structure
    display_sample_analysis()
    
    # Run comprehensive test
    success = test_enhanced_analysis()
    
    if success:
        print("\n✅ All tests passed! Enhanced features are working correctly.")
        print("\n📈 NEW FEATURES SUMMARY:")
        print("   • Professional job descriptions and prompts")
        print("   • Detailed skill matching and gap analysis")
        print("   • Experience relevance assessment")
        print("   • Growth potential scoring")
        print("   • Cultural fit indicators")
        print("   • Interview focus area suggestions")
        print("   • Enhanced export columns")
        print("   • Red flag identification")
        print("   • Salary expectation alignment")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        sys.exit(1)
